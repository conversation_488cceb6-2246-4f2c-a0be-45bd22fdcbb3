-- Infiltration Spawner Entity - Client Side

include("shared.lua")

function ENT:Initialize()
    self.NextParticle = 0
    self.GlowSprite = Material("sprites/light_glow02_add")
end

function ENT:Draw()
    self:DrawModel()
    
    local pos = self:GetPos() + Vector(0, 0, 80)
    local ang = LocalPlayer():EyeAngles()
    
    if not ang then ang = Angle(0, 0, 0) end
    
    local drawAng = Angle(ang.p, ang.y, ang.r)
    drawAng:RotateAroundAxis(drawAng:Forward(), 90)
    drawAng:RotateAroundAxis(drawAng:Right(), 90)
    
    cam.Start3D2D(pos, drawAng, 0.2)
        draw.SimpleTextOutlined("Infiltration Mk IV", "DermaLarge", 0, 0, Color(100, 150, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 2, Color(0, 0, 0))
        draw.SimpleTextOutlined("Combinaison Furtive", "DermaDefault", 0, 30, Color(150, 150, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
        draw.SimpleTextOutlined("Camouflage: Optique | Protection: Aucune", "DermaDefault", 0, 50, Color(200, 200, 200), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
        draw.SimpleTextOutlined("Invisibilité: 3s immobile", "DermaDefault", 0, 65, Color(200, 200, 200), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
        draw.SimpleTextOutlined("Appuyez sur E pour équiper", "DermaDefault", 0, 90, Color(100, 255, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
    cam.End3D2D()
    
    if CurTime() > self.NextParticle then
        self.NextParticle = CurTime() + 2
        
        local effectdata = EffectData()
        effectdata:SetOrigin(self:GetPos() + Vector(0, 0, 40))
        effectdata:SetScale(0.5)
        util.Effect("ManhackSparks", effectdata)
    end
end

function ENT:Think()
    local pulse = math.sin(CurTime() * 3) * 0.4 + 0.6
    local baseColor = Color(100 * pulse, 100 * pulse, 120 * pulse, 255)
    self:SetColor(baseColor)
    
    self:SetNextClientThink(CurTime() + 0.1)
    return true
end

function ENT:DrawTranslucent()
    if not self.GlowSprite then return end
    
    render.SetMaterial(self.GlowSprite)
    render.DrawSprite(self:GetPos() + Vector(0, 0, 40), 80, 80, Color(100, 150, 255, 120))
    render.DrawSprite(self:GetPos() + Vector(15, 15, 25), 25, 25, Color(150, 150, 255, 80))
    render.DrawSprite(self:GetPos() + Vector(-15, -15, 25), 25, 25, Color(150, 150, 255, 80))
end
