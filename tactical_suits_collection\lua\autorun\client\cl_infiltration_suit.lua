-- Infiltration Mk IV Suit - Client Side
-- Interface HUD pour la combinaison de camouflage

-- Configuration client
INFILTRATION_SUIT_CLIENT = INFILTRATION_SUIT_CLIENT or {}
INFILTRATION_SUIT_CLIENT.Active = false
INFILTRATION_SUIT_CLIENT.CamoStatus = "visible"

-- Couleurs de l'interface (thème gris/bleu)
local COLOR_BG = Color(20, 20, 30, 200)
local COLOR_BORDER = Color(100, 150, 255, 255)
local COLOR_TEXT = Color(255, 255, 255, 255)
local COLOR_CAMO = Color(100, 200, 255, 255)

-- Fonction pour dessiner l'HUD
function INFILTRATION_SUIT_CLIENT:DrawHUD()
    if not self.Active then return end
    
    local scrW, scrH = ScrW(), ScrH()
    
    -- Panneau principal (position droite)
    local panelW, panelH = 250, 120
    local x, y = scrW - panelW - 20, scrH/2 - panelH/2
    
    draw.RoundedBox(8, x, y, panelW, panelH, COLOR_BG)
    draw.RoundedBox(8, x, y, panelW, 25, COLOR_BORDER)
    
    -- Titre
    draw.SimpleText("INFILTRATION MK IV", "DermaDefault", x + panelW/2, y + 12, COLOR_TEXT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Ligne de séparation
    draw.RoundedBox(0, x + 10, y + 30, panelW - 20, 1, COLOR_BORDER)
    
    -- Statut du camouflage
    local statusText = "CAMOUFLAGE: "
    local statusColor = COLOR_CAMO
    
    if self.CamoStatus == "invisible" then
        statusText = statusText .. "ACTIF"
        statusColor = Color(100, 255, 100)
    elseif self.CamoStatus == "partial" then
        statusText = statusText .. "PARTIEL"
        statusColor = Color(255, 200, 100)
    else
        statusText = statusText .. "INACTIF"
        statusColor = Color(255, 100, 100)
    end
    
    draw.SimpleText(statusText, "DermaDefault", x + 10, y + 40, statusColor)
    
    -- Instructions
    draw.SimpleText("Restez immobile pour", "DermaDefault", x + 10, y + 60, COLOR_TEXT)
    draw.SimpleText("activer le camouflage", "DermaDefault", x + 10, y + 75, COLOR_TEXT)
    
    -- Indicateur visuel du camouflage
    if self.CamoStatus == "invisible" then
        local pulse = math.sin(CurTime() * 4) * 0.3 + 0.7
        draw.SimpleText(">>> INVISIBLE <<<", "DermaDefault", x + panelW/2, y + 95, Color(100 * pulse, 255 * pulse, 100 * pulse), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
end

-- Fonction pour activer la combinaison
function INFILTRATION_SUIT_CLIENT:Activate()
    self.Active = true
    self.CamoStatus = "visible"
    
    -- Désactiver les autres HUDs
    if UTS_SUIT_CLIENT then UTS_SUIT_CLIENT.Active = false end
    if COMBAT_SUIT_CLIENT then COMBAT_SUIT_CLIENT.Active = false end
    if HEAVY_ARMOR_CLIENT then HEAVY_ARMOR_CLIENT.Active = false end
    
    chat.AddText(Color(100, 150, 255), "[Infiltration Mk IV] ", Color(255, 255, 255), "Systèmes de camouflage activés")
end

-- Fonction pour désactiver la combinaison
function INFILTRATION_SUIT_CLIENT:Deactivate()
    self.Active = false
    self.CamoStatus = "visible"
    
    chat.AddText(Color(255, 100, 100), "[Infiltration Mk IV] ", Color(255, 255, 255), "Systèmes de camouflage désactivés")
end

-- Hook pour dessiner le HUD
hook.Add("HUDPaint", "INFILTRATION_DrawHUD", function()
    INFILTRATION_SUIT_CLIENT:DrawHUD()
end)

-- Désactiver automatiquement la combinaison quand le joueur meurt
hook.Add("PlayerDeath", "INFILTRATION_ClientPlayerDeath", function(victim, inflictor, attacker)
    if victim == LocalPlayer() and INFILTRATION_SUIT_CLIENT.Active then
        INFILTRATION_SUIT_CLIENT:Deactivate()
    end
end)

-- Netmessages
net.Receive("INFILTRATION_Equip", function()
    INFILTRATION_SUIT_CLIENT:Activate()
end)

net.Receive("INFILTRATION_Unequip", function()
    INFILTRATION_SUIT_CLIENT:Deactivate()
end)

net.Receive("INFILTRATION_CamoUpdate", function()
    local status = net.ReadString()
    INFILTRATION_SUIT_CLIENT.CamoStatus = status
end)

print("Infiltration Mk IV Suit - Client loaded")
