-- Heavy Combat Armor Mk III - Server Side
-- Armure de combat lourde portable avec protection totale

-- Configuration
HEAVY_ARMOR = HEAVY_ARMOR or {}
HEAVY_ARMOR.Config = {
    ArmorHealth = 300, -- Points de vie de l'armure
    PlayerArmor = 200, -- Armure du joueur
    MoveSpeedMultiplier = 0.7, -- Réduction de vitesse (armure lourde)
    JumpPowerMultiplier = 1.5, -- Augmentation du saut (servos hydrauliques)
    RegenRate = 2, -- Régénération d'armure par seconde
    MaxRegenDelay = 5, -- D<PERSON>lai avant régénération après dégâts
}

-- Table des joueurs équipés
HEAVY_ARMOR.EquippedPlayers = HEAVY_ARMOR.EquippedPlayers or {}

-- Fonction pour équiper l'armure lourde
function HEAVY_ARMOR:EquipPlayer(ply)
    if not IsValid(ply) then return false end
    
    -- Vérifier si le joueur porte déjà une autre combinaison
    if UTS_SUIT and UTS_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange d'équipement : UTS MkVII → Armure Lourde Mk III")
        UTS_SUIT:UnequipPlayer(ply)
    elseif INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange d'équipement : Infiltration Mk IV → Armure Lourde Mk III")
        INFILTRATION_SUIT:UnequipPlayer(ply)
    elseif COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange d'équipement : Combat Mk II → Armure Lourde Mk III")
        COMBAT_SUIT:UnequipPlayer(ply)
    end
    
    -- Vérifier si le joueur porte déjà l'armure lourde
    if self.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Vous portez déjà l'Armure Lourde Mk III!")
        return false
    end
    
    -- Équiper l'armure
    local armorData = {
        health = self.Config.ArmorHealth,
        maxHealth = self.Config.ArmorHealth,
        lastDamageTime = 0,
        originalSpeed = ply:GetWalkSpeed(),
        originalRunSpeed = ply:GetRunSpeed(),
        originalJumpPower = ply:GetJumpPower(),
        originalColor = ply:GetColor(),
        originalScale = ply:GetModelScale()
    }
    
    self.EquippedPlayers[ply:SteamID()] = armorData
    
    -- Appliquer les effets de l'armure
    ply:SetArmor(self.Config.PlayerArmor)
    ply:SetWalkSpeed(armorData.originalSpeed * self.Config.MoveSpeedMultiplier)
    ply:SetRunSpeed(armorData.originalRunSpeed * self.Config.MoveSpeedMultiplier)
    ply:SetJumpPower(armorData.originalJumpPower * self.Config.JumpPowerMultiplier)
    
    -- Changer l'apparence du joueur (couleur orange pour simuler l'armure)
    ply:SetColor(Color(255, 150, 0, 255))
    ply:SetModelScale(1.2, 0) -- Légèrement plus grand
    
    -- Envoyer les données au client
    net.Start("HEAVY_ARMOR_Equip")
    net.WriteInt(armorData.health, 16)
    net.WriteInt(armorData.maxHealth, 16)
    net.Send(ply)
    
    ply:ChatPrint("Armure Lourde Mk III activée - Systèmes de protection opérationnels")
    
    return true
end

-- Fonction pour déséquiper l'armure
function HEAVY_ARMOR:UnequipPlayer(ply)
    if not IsValid(ply) then return false end
    
    local armorData = self.EquippedPlayers[ply:SteamID()]
    if not armorData then
        ply:ChatPrint("Vous ne portez pas l'Armure Lourde Mk III!")
        return false
    end
    
    -- Restaurer les paramètres originaux
    ply:SetWalkSpeed(armorData.originalSpeed)
    ply:SetRunSpeed(armorData.originalRunSpeed)
    ply:SetJumpPower(armorData.originalJumpPower)
    ply:SetColor(armorData.originalColor)
    ply:SetModelScale(armorData.originalScale, 0)
    ply:SetArmor(0)
    
    -- Retirer de la liste
    self.EquippedPlayers[ply:SteamID()] = nil
    
    -- Envoyer au client
    net.Start("HEAVY_ARMOR_Unequip")
    net.Send(ply)
    
    ply:ChatPrint("Armure Lourde Mk III désactivée")
    
    return true
end

-- Hook pour gérer les dégâts (protection totale)
hook.Add("EntityTakeDamage", "HEAVY_ARMOR_DamageReduction", function(target, dmginfo)
    if not IsValid(target) or not target:IsPlayer() then return end
    
    local armorData = HEAVY_ARMOR.EquippedPlayers[target:SteamID()]
    if not armorData then return end
    
    local damage = dmginfo:GetDamage()
    
    -- Tant qu'il y a de l'armure, les PV ne descendent pas
    if armorData.health > 0 then
        -- Tous les dégâts vont à l'armure
        armorData.health = math.max(0, armorData.health - damage)
        armorData.lastDamageTime = CurTime()
        
        -- Annuler complètement les dégâts au joueur
        dmginfo:SetDamage(0)
        
        -- Envoyer les nouvelles données au client
        net.Start("HEAVY_ARMOR_UpdateHealth")
        net.WriteInt(armorData.health, 16)
        net.Send(target)
        
        -- Si l'armure est détruite, la retirer
        if armorData.health <= 0 then
            target:ChatPrint("ALERTE: Armure lourde détruite - Protection perdue!")
            timer.Simple(0.5, function()
                if IsValid(target) then
                    HEAVY_ARMOR:UnequipPlayer(target)
                end
            end)
        end
    end
end)

-- Hook pour la régénération d'armure
hook.Add("Think", "HEAVY_ARMOR_Regeneration", function()
    for steamID, armorData in pairs(HEAVY_ARMOR.EquippedPlayers) do
        local ply = player.GetBySteamID(steamID)
        if IsValid(ply) and armorData.health < armorData.maxHealth then
            -- Régénérer après le délai
            if CurTime() - armorData.lastDamageTime > HEAVY_ARMOR.Config.MaxRegenDelay then
                armorData.health = math.min(armorData.maxHealth, armorData.health + HEAVY_ARMOR.Config.RegenRate * FrameTime())
                
                -- Envoyer les nouvelles données au client
                net.Start("HEAVY_ARMOR_UpdateHealth")
                net.WriteInt(armorData.health, 16)
                net.Send(ply)
            end
        end
    end
end)

-- Nettoyage à la déconnexion
hook.Add("PlayerDisconnected", "HEAVY_ARMOR_Cleanup", function(ply)
    if HEAVY_ARMOR.EquippedPlayers[ply:SteamID()] then
        HEAVY_ARMOR.EquippedPlayers[ply:SteamID()] = nil
    end
end)

-- Nettoyage à la mort
hook.Add("PlayerDeath", "HEAVY_ARMOR_Death", function(victim, inflictor, attacker)
    if HEAVY_ARMOR.EquippedPlayers[victim:SteamID()] then
        HEAVY_ARMOR:UnequipPlayer(victim)
    end
end)

-- Netmessages
util.AddNetworkString("HEAVY_ARMOR_Equip")
util.AddNetworkString("HEAVY_ARMOR_Unequip")
util.AddNetworkString("HEAVY_ARMOR_UpdateHealth")
util.AddNetworkString("HEAVY_ARMOR_RequestEquip")
util.AddNetworkString("HEAVY_ARMOR_RequestUnequip")

-- Recevoir les demandes d'équipement
net.Receive("HEAVY_ARMOR_RequestEquip", function(len, ply)
    if not IsValid(ply) then return end
    
    -- Vérifier si le joueur porte une autre combinaison
    if (INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()]) or
       (COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[ply:SteamID()]) or
       (UTS_SUIT and UTS_SUIT.EquippedPlayers[ply:SteamID()]) then
        ply:ChatPrint("Vous portez déjà un équipement. Utilisez !heavy unequip d'abord ou utilisez un spawner pour échanger.")
        return
    end
    
    HEAVY_ARMOR:EquipPlayer(ply)
end)

net.Receive("HEAVY_ARMOR_RequestUnequip", function(len, ply)
    HEAVY_ARMOR:UnequipPlayer(ply)
end)

print("Heavy Combat Armor Mk III - Server loaded")
