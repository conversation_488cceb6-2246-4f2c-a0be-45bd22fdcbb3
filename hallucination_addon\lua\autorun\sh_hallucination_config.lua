-- Hallucination Addon Configuration
-- Configuration partagée entre client et serveur

-- Types d'hallucinations disponibles
HALLUCINATION_TYPES = {
    VISUAL_DISTORTION = 1,  -- Distorsions visuelles, tremblements d'écran
    FAKE_ENTITIES = 2,      -- Entités factices qui apparaissent
    AUDIO_HALLUCINATION = 3, -- Sons fantômes et chuchotements
    SCREEN_EFFECTS = 4,     -- Effets d'écran (bruit, lignes de scan)
    FAKE_PLAYERS = 5,       -- Faux joueurs qui apparaissent
    PARANOIA_MODE = 6       -- Mode paranoïa complet (combine plusieurs effets)
}

-- Durées prédéfinies (en secondes)
HALLUCINATION_DURATIONS = {
    SHORT = 10,     -- Courte hallucination
    MEDIUM = 30,    -- Hallucination moyenne
    LONG = 60,      -- Longue hallucination
    EXTREME = 120   -- Hallucination extrême
}

-- Configuration des effets
HALLUCINATION_CONFIG = {
    -- Intensité maximale des effets (0.0 à 2.0)
    MAX_INTENSITY = 2.0,
    
    -- Nombre maximum d'entités factices simultanées
    MAX_FAKE_ENTITIES = 10,
    
    -- Nombre maximum de faux joueurs simultanés
    MAX_FAKE_PLAYERS = 5,
    
    -- Distance maximale pour les entités factices
    FAKE_ENTITY_DISTANCE = 500,
    
    -- Distance maximale pour les faux joueurs
    FAKE_PLAYER_DISTANCE = 300,
    
    -- Fréquence des effets audio (en secondes)
    AUDIO_FREQUENCY_MIN = 5,
    AUDIO_FREQUENCY_MAX = 20,
    
    -- Volume des effets audio (0.0 à 1.0)
    AUDIO_VOLUME_MIN = 0.1,
    AUDIO_VOLUME_MAX = 0.6,
    
    -- Intensité du tremblement d'écran
    SCREEN_SHAKE_INTENSITY = 0.1,
    
    -- Fréquence des messages de paranoïa (en secondes)
    PARANOIA_MESSAGE_FREQUENCY_MIN = 10,
    PARANOIA_MESSAGE_FREQUENCY_MAX = 30,
    
    -- Durée de vie des entités factices (en secondes)
    FAKE_ENTITY_LIFETIME = 15,
    
    -- Durée de vie des faux joueurs (en secondes)
    FAKE_PLAYER_LIFETIME = 20
}

-- Messages d'hallucination personnalisables
HALLUCINATION_MESSAGES = {
    START = {
        "La réalité commence à se distordre...",
        "Quelque chose ne va pas...",
        "Votre vision se trouble...",
        "Le monde autour de vous change...",
        "Vous sentez que quelque chose cloche..."
    },
    
    END = {
        "La réalité revient lentement...",
        "Votre vision s'éclaircit...",
        "Le monde redevient normal...",
        "L'hallucination se dissipe...",
        "Vous reprenez vos esprits..."
    },
    
    PARANOIA = {
        "Quelqu'un vous observe...",
        "Vous entendez des pas derrière vous...",
        "Une ombre bouge dans votre vision périphérique...",
        "Vous n'êtes pas seul...",
        "Ils savent que vous êtes là...",
        "Quelque chose ne va pas...",
        "Vous sentez une présence...",
        "Regardez derrière vous...",
        "Ils arrivent...",
        "Vous ne pouvez pas leur échapper...",
        "Quelqu'un murmure votre nom...",
        "Vous êtes suivi...",
        "Ils vous ont trouvé...",
        "Cachez-vous...",
        "Ne faites pas de bruit..."
    }
}

-- Modèles d'entités factices
HALLUCINATION_FAKE_MODELS = {
    "models/props_c17/oildrum001.mdl",
    "models/props_c17/chair_office01a.mdl",
    "models/props_c17/suitcase_passenger_physics.mdl",
    "models/props_c17/briefcase001a.mdl",
    "models/props_c17/lampshade001a.mdl",
    "models/props_junk/cardboard_box004a.mdl",
    "models/props_junk/garbage_bag001a.mdl",
    "models/props_trainstation/trashcan_indoor001b.mdl",
    "models/props_wasteland/controlroom_chair001a.mdl",
    "models/props_wasteland/kitchen_shelf001a.mdl"
}

-- Sons d'hallucination
HALLUCINATION_SOUNDS = {
    AMBIENT = {
        "ambient/voices/crying_loop1.wav",
        "ambient/atmosphere/underground_hall_loop1.wav",
        "ambient/wind/wind_snippet1.wav",
        "ambient/atmosphere/cave_hit1.wav",
        "ambient/atmosphere/cave_hit2.wav",
        "ambient/atmosphere/cave_hit3.wav",
        "ambient/atmosphere/cave_hit4.wav"
    },
    
    VOICES = {
        "ambient/voices/m_scream1.wav",
        "ambient/voices/playground_memory.wav",
        "vo/npc/male01/hacks01.wav",
        "vo/npc/male01/hacks02.wav",
        "vo/npc/female01/hacks01.wav",
        "vo/npc/female01/hacks02.wav"
    },
    
    CREATURES = {
        "ambient/creatures/town_zombie_call1.wav",
        "npc/zombie/zombie_voice_idle1.wav",
        "npc/zombie/zombie_voice_idle2.wav",
        "npc/zombie/zombie_voice_idle3.wav",
        "npc/zombie/zombie_voice_idle4.wav"
    }
}

-- Fonction pour obtenir un message aléatoire
function GetRandomHallucinationMessage(messageType)
    local messages = HALLUCINATION_MESSAGES[messageType]
    if messages and #messages > 0 then
        return messages[math.random(1, #messages)]
    end
    return "..."
end

-- Fonction pour obtenir un modèle factice aléatoire
function GetRandomFakeModel()
    return HALLUCINATION_FAKE_MODELS[math.random(1, #HALLUCINATION_FAKE_MODELS)]
end

-- Fonction pour obtenir un son d'hallucination aléatoire
function GetRandomHallucinationSound(soundType)
    local sounds = HALLUCINATION_SOUNDS[soundType]
    if sounds and #sounds > 0 then
        return sounds[math.random(1, #sounds)]
    end
    return nil
end

print("[Hallucination Addon] Configuration loaded successfully!")
