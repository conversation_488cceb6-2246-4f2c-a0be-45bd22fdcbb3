local squares = {}
local texts = {}
local corrupted = false
local camera_shake_intensity = 0
local camera_shake_time = 0
local blackout_alpha = 0
local blackout_start_time = 0

local corruption_phrases = {
    "CORROMPU",
    "INFECTÉ",
    "TÉNÈBRES",
    "VIDE",
    "CORRUPTION",
    "TAINTÉ",
    "OMBRE",
    "FONDU"
}

local corruption_overlay_alpha = 0
local corruption_pulse = 0
local corruption_noise = {}

-- Création des fonts personnalisées au début du fichier
surface.CreateFont("CorruptionFont", {
    font = "Who asks Satan",
    size = 32,
    weight = 700,
    antialias = true,
    shadow = true,
    outline = true
})

surface.CreateFont("CorruptionFontLarge", {
    font = "Who asks Satan", 
    size = 48,
    weight = 800,
    antialias = true,
    shadow = true,
    outline = true
})

surface.CreateFont("CorruptionFontSmall", {
    font = "Who asks Satan",
    size = 24,
    weight = 600,
    antialias = true,
    shadow = true,
    outline = true
})

surface.CreateFont("CorruptionFontHuge", {
    font = "Who asks Satan",
    size = 64,
    weight = 900,
    antialias = true,
    shadow = true,
    outline = true
})

-- Générer du bruit pour l'effet de corruption
local function generate_corruption_noise()
    corruption_noise = {}
    for i = 1, 50 do
        table.insert(corruption_noise, {
            x = math.random(0, ScrW()),
            y = math.random(0, ScrH()),
            w = math.random(5, 30),
            h = math.random(5, 30),
            alpha = math.random(50, 150),
            speed = math.random(1, 5)
        })
    end
end

-- Générer des glitches de corruption
local function generate_corruption_glitches()
    corruption_glitches = {}
    for i = 1, 30 do
        table.insert(corruption_glitches, {
            x = math.random(0, ScrW()),
            y = math.random(0, ScrH()),
            w = math.random(50, 200),
            h = math.random(5, 20),
            offset_x = 0,
            glitch_timer = 0,
            type = math.random(1, 3)
        })
    end
end

-- Générer des veines de corruption
local function generate_corruption_veins()
    corruption_veins = {}
    for i = 1, 15 do
        local start_x = math.random(0, ScrW())
        local start_y = math.random(0, ScrH())
        local vein = {
            points = {{x = start_x, y = start_y}},
            thickness = math.random(2, 8),
            alpha = math.random(100, 200),
            growth_speed = math.random(50, 150),
            direction = math.random() * math.pi * 2,
            branches = {}
        }
        
        -- Créer les points de la veine
        for j = 1, math.random(20, 50) do
            local last_point = vein.points[#vein.points]
            vein.direction = vein.direction + (math.random() - 0.5) * 0.5
            local new_x = last_point.x + math.cos(vein.direction) * math.random(10, 30)
            local new_y = last_point.y + math.sin(vein.direction) * math.random(10, 30)
            table.insert(vein.points, {x = new_x, y = new_y})
        end
        
        table.insert(corruption_veins, vein)
    end
end

local function add_square()
    local x = math.random(0, ScrW() - 100)
    local y = math.random(0, ScrH() - 100)
    local w = math.random(15, 120)
    local h = math.random(15, 120)
    
    table.insert(squares, {
        x = x, 
        y = y, 
        w = w, 
        h = h, 
        time = CurTime(),
        original_x = x,
        original_y = y,
        rotation = math.random(0, 360),
        rotation_speed = math.random(-3, 3),
        scale = 1,
        scale_speed = math.random(-0.5, 0.5),
        alpha = 255,
        fade_speed = math.random(1, 3),
        tremble_intensity = math.random(3, 12),
        color_shift = math.random(0, 50),
        growth_phase = 0,
        type = math.random(1, 4) -- Différents types de carrés
    })
end

-- Fonction pour créer un texte qui devient fou (lettres qui changent rapidement)
local function add_crazy_text()
    local base_phrases = {
        "JE DEVIENS FOU",
        "AIDE MOI",
        "CA FAIT MAL",
        "JE NE CONTROLE PLUS",
        "ARRETEZ CA",
        "QU EST CE QUI M ARRIVE",
        "JE PERDS LA RAISON",
        "QUELQU UN",
        "SAUVEZ MOI"
    }

    local phrase = base_phrases[math.random(#base_phrases)]
    local x = math.random(0, ScrW() - 300)
    local y = math.random(0, ScrH() - 100)

    local fonts = {"CorruptionFontSmall", "CorruptionFont", "CorruptionFontLarge", "CorruptionFontHuge"}
    local selected_font = fonts[math.random(#fonts)]

    table.insert(texts, {
        text = phrase,
        original_text = phrase,
        x = x,
        y = y,
        time = CurTime(),
        last_update = CurTime(),
        font = selected_font,
        effect = "crazy",
        crazy_speed = math.random(5, 15), -- Vitesse de changement des lettres
        crazy_intensity = math.random(30, 80), -- Pourcentage de lettres qui changent
        last_crazy_update = CurTime()
    })
end

local function add_text()
    local phrase = corruption_phrases[math.random(#corruption_phrases)]
    local x = math.random(0, ScrW() - 200)
    local y = math.random(0, ScrH() - 50)

    -- Choix aléatoire de la taille de font avec la nouvelle font énorme
    local fonts = {"CorruptionFontSmall", "CorruptionFont", "CorruptionFontLarge", "CorruptionFontHuge"}
    local selected_font = fonts[math.random(#fonts)]

    table.insert(texts, {
        text = phrase,
        x = x,
        y = y,
        time = CurTime(),
        last_update = CurTime(),
        font = selected_font
    })
end

net.Receive("CorruptPlayer", function()
    if timer.Exists("CorruptionEffect") then return end
    corrupted = true
    corruption_overlay_alpha = 0
    corruption_pulse = 0
    corruption_intensity = 0
    corruption_start_time = CurTime()
    
    generate_corruption_noise()
    generate_corruption_glitches()
    generate_corruption_veins()
    
    -- Jouer le son de corruption
    surface.PlaySound("Infection.wav")
    
    timer.Create("CorruptionEffect", 0.8, 159, function()
        add_square()
        if math.random() < 0.4 then
            add_text()
        end
        -- Ajouter des textes fous de temps en temps
        if math.random() < 0.15 then
            add_crazy_text()
        end
        -- Régénérer occasionnellement les effets
        if math.random() < 0.2 then
            generate_corruption_noise()
        end
    end)

    -- Sons de craquement d'os et de douleur à 1 minute 39 secondes (99 secondes)
    timer.Simple(99, function()
        if corrupted then
            -- Sons de craquement d'os
            surface.PlaySound("physics/body/body_medium_break2.wav")

            timer.Simple(0.5, function()
                if corrupted then
                    surface.PlaySound("physics/body/body_medium_break3.wav")
                end
            end)

            timer.Simple(1.2, function()
                if corrupted then
                    surface.PlaySound("physics/body/body_medium_break4.wav")
                end
            end)

            -- Sons de douleur/cris
            timer.Simple(2, function()
                if corrupted then
                    surface.PlaySound("vo/npc/male01/pain07.wav")
                end
            end)

            timer.Simple(3.5, function()
                if corrupted then
                    surface.PlaySound("vo/npc/male01/pain08.wav")
                end
            end)

            timer.Simple(5, function()
                if corrupted then
                    surface.PlaySound("vo/npc/male01/pain09.wav")
                end
            end)

            -- Sons d'os qui craquent de manière plus intense
            timer.Simple(6.5, function()
                if corrupted then
                    surface.PlaySound("physics/body/body_medium_break1.wav")
                end
            end)

            timer.Simple(8, function()
                if corrupted then
                    surface.PlaySound("physics/flesh/flesh_bloody_break.wav")
                end
            end)
        end
    end)

    -- Commencer le fondu vers le noir à 110 secondes (17 secondes avant la fin)
    timer.Simple(110, function()
        if corrupted then
            blackout_start_time = CurTime()
        end
    end)

    timer.Simple(127, function()
        corrupted = false
        corruption_overlay_alpha = 0
        corruption_intensity = 0
        blackout_alpha = 0
        blackout_start_time = 0

        -- Envoyer un signal au serveur pour tuer le joueur et spawner un fast zombie
        net.Start("CorruptionEnd")
        net.SendToServer()
    end)
end)

hook.Add("HUDPaint", "TremblingSquares", function()
    for i = #squares, 1, -1 do
        local sq = squares[i]
        local elapsed = CurTime() - sq.time
        
        if elapsed > 12 then
            table.remove(squares, i)
        else
            -- Tremblement amélioré avec intensité variable
            local tremble_x = math.sin(CurTime() * 8 + i * 0.7) * sq.tremble_intensity
            local tremble_y = math.cos(CurTime() * 6 + i * 0.5) * sq.tremble_intensity
            
            -- Rotation progressive
            sq.rotation = sq.rotation + sq.rotation_speed
            
            -- Scaling pulsant
            sq.scale = 1 + math.sin(CurTime() * 4 + i) * 0.3 + sq.scale_speed * elapsed * 0.1
            sq.scale = math.max(0.2, sq.scale)
            
            -- Fade out progressif
            sq.alpha = math.max(0, 255 - (elapsed / 12) * 255 * sq.fade_speed)
            
            -- Phase de croissance initiale
            if elapsed < 2 then
                sq.growth_phase = elapsed / 2
            else
                sq.growth_phase = 1
            end
            
            -- Position finale avec tremblement
            local final_x = sq.x + tremble_x
            local final_y = sq.y + tremble_y
            local final_w = sq.w * sq.scale * sq.growth_phase
            local final_h = sq.h * sq.scale * sq.growth_phase
            
            -- Couleur avec variations
            local color_variation = math.sin(CurTime() * 5 + i) * sq.color_shift
            local red = math.max(0, color_variation)
            local green = 0
            local blue = 0
            
            if sq.type == 1 then
                -- Carré noir classique
                surface.SetDrawColor(red, green, blue, sq.alpha)
                surface.DrawRect(final_x, final_y, final_w, final_h)
                
            elseif sq.type == 2 then
                -- Carré avec bordure rouge
                surface.SetDrawColor(red + 100, 0, 0, sq.alpha)
                surface.DrawOutlinedRect(final_x, final_y, final_w, final_h)
                surface.SetDrawColor(red, green, blue, sq.alpha * 0.7)
                surface.DrawRect(final_x + 2, final_y + 2, final_w - 4, final_h - 4)
                
            elseif sq.type == 3 then
                -- Carré fragmenté
                local fragments = 4
                local frag_w = final_w / fragments
                local frag_h = final_h / fragments
                
                for fx = 0, fragments - 1 do
                    for fy = 0, fragments - 1 do
                        if math.random() < 0.7 then
                            local frag_x = final_x + fx * frag_w + math.random(-3, 3)
                            local frag_y = final_y + fy * frag_h + math.random(-3, 3)
                            surface.SetDrawColor(red, green, blue, sq.alpha)
                            surface.DrawRect(frag_x, frag_y, frag_w - 1, frag_h - 1)
                        end
                    end
                end
                
            else
                -- Carré avec effet de glitch
                surface.SetDrawColor(red, green, blue, sq.alpha)
                surface.DrawRect(final_x, final_y, final_w, final_h)
                
                -- Glitch horizontal
                if math.random() < 0.3 then
                    local glitch_offset = math.random(-10, 10)
                    surface.SetDrawColor(red + 50, 0, 0, sq.alpha * 0.5)
                    surface.DrawRect(final_x + glitch_offset, final_y, final_w, final_h * 0.3)
                end
                
                -- Glitch vertical
                if math.random() < 0.2 then
                    local glitch_offset = math.random(-8, 8)
                    surface.SetDrawColor(red + 30, 0, 0, sq.alpha * 0.4)
                    surface.DrawRect(final_x, final_y + glitch_offset, final_w * 0.4, final_h)
                end
            end
            
            -- Effet d'aura corrompue
            if sq.type == 1 and math.random() < 0.1 then
                surface.SetDrawColor(100, 0, 0, 30)
                surface.DrawRect(final_x - 5, final_y - 5, final_w + 10, final_h + 10)
            end
            
            -- Particules qui s'échappent du carré
            if math.random() < 0.05 then
                local particle_x = final_x + math.random(0, final_w)
                local particle_y = final_y + math.random(0, final_h)
                surface.SetDrawColor(red + 100, 0, 0, 100)
                surface.DrawRect(particle_x, particle_y, 2, 2)
            end
        end
    end
    
    -- Textes avec la font Who asks Satan et effets spéciaux
    for i = #texts, 1, -1 do
        local txt = texts[i]
        if CurTime() - txt.time > 10 then
            table.remove(texts, i)
        else
            if CurTime() - txt.last_update > 0.5 then
                txt.x = math.random(0, ScrW() - 200)
                txt.y = math.random(0, ScrH() - 50)
                txt.last_update = CurTime()
            end

            -- Effet spécial pour les textes "crazy"
            if txt.effect == "crazy" then
                -- Changer les lettres rapidement
                if CurTime() - txt.last_crazy_update > (1 / txt.crazy_speed) then
                    local crazy_text = ""
                    local random_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()[]{}|\\:;\"'<>,.?/~`"

                    for j = 1, #txt.original_text do
                        local original_char = string.sub(txt.original_text, j, j)

                        if original_char == " " then
                            -- Garder les espaces
                            crazy_text = crazy_text .. " "
                        elseif math.random(100) <= txt.crazy_intensity then
                            -- Remplacer par un caractère aléatoire
                            crazy_text = crazy_text .. string.sub(random_chars, math.random(#random_chars), math.random(#random_chars))
                        else
                            -- Garder le caractère original
                            crazy_text = crazy_text .. original_char
                        end
                    end

                    txt.text = crazy_text
                    txt.last_crazy_update = CurTime()
                end

                -- Couleur qui clignote pour l'effet de folie
                local color_intensity = math.abs(math.sin(CurTime() * 10)) * 255
                local tremble_x = math.sin(CurTime() * 15 + i * 0.5) * 8
                local tremble_y = math.cos(CurTime() * 12 + i * 0.5) * 8

                surface.SetFont(txt.font or "CorruptionFont")
                surface.SetTextColor(color_intensity, color_intensity * 0.3, 0, 255)
                surface.SetTextPos(txt.x + tremble_x, txt.y + tremble_y)
                surface.DrawText(txt.text)
            else
                -- Texte normal avec tremblement
                local tremble_x = math.sin(CurTime() * 5 + i * 0.5) * 5
                local tremble_y = math.cos(CurTime() * 5 + i * 0.5) * 5
                surface.SetFont(txt.font or "CorruptionFont")
                surface.SetTextColor(255, 0, 0, 255)
                surface.SetTextPos(txt.x + tremble_x, txt.y + tremble_y)
                surface.DrawText(txt.text)
            end
        end
    end
end)

-- Nouveau hook pour l'effet de fondu de corruption
hook.Add("HUDPaint", "CorruptionOverlay", function()
    if not corrupted then return end
    
    local time = CurTime()
    
    -- Augmentation progressive de l'overlay
    corruption_overlay_alpha = math.min(corruption_overlay_alpha + FrameTime() * 30, 80)
    corruption_pulse = math.sin(time * 3) * 20
    
    -- Fond de corruption avec pulsation
    local overlay_alpha = corruption_overlay_alpha + corruption_pulse
    surface.SetDrawColor(0, 0, 0, overlay_alpha)
    surface.DrawRect(0, 0, ScrW(), ScrH())
    
    -- Overlay rouge sanglant
    surface.SetDrawColor(100, 0, 0, overlay_alpha * 0.3)
    surface.DrawRect(0, 0, ScrW(), ScrH())
    
    -- Bruit de corruption (pixels corrompus)
    for i, noise in ipairs(corruption_noise) do
        noise.y = noise.y + noise.speed
        if noise.y > ScrH() then
            noise.y = -noise.h
            noise.x = math.random(0, ScrW())
        end
        
        local flicker = math.sin(time * 10 + i) * 0.5 + 0.5
        surface.SetDrawColor(255, 0, 0, noise.alpha * flicker)
        surface.DrawRect(noise.x, noise.y, noise.w, noise.h)
    end
    
    -- Lignes de corruption horizontales
    for i = 1, 10 do
        local y = math.random(0, ScrH())
        local alpha = math.sin(time * 5 + i) * 50 + 100
        surface.SetDrawColor(255, 0, 0, alpha)
        surface.DrawRect(0, y, ScrW(), 2)
    end
    
    -- Effet de vignette corrompue
    local center_x, center_y = ScrW() / 2, ScrH() / 2
    for i = 1, 5 do
        local radius = i * 200
        local alpha = (6 - i) * 10
        surface.SetDrawColor(0, 0, 0, alpha)
        surface.DrawOutlinedRect(center_x - radius, center_y - radius, radius * 2, radius * 2)
    end
end)

-- Hook pour l'effet de blackout (fondu vers le noir complet)
hook.Add("HUDPaint", "CorruptionBlackout", function()
    if not corrupted or blackout_start_time == 0 then return end

    local elapsed_blackout = CurTime() - blackout_start_time
    local blackout_duration = 17 -- 17 secondes pour le fondu complet

    -- Calculer l'alpha du blackout (progression du fondu)
    if elapsed_blackout < blackout_duration then
        -- Fondu progressif vers le noir
        blackout_alpha = math.min(255, (elapsed_blackout / blackout_duration) * 255)

        -- Accélération du fondu vers la fin
        local progress = elapsed_blackout / blackout_duration
        if progress > 0.7 then
            -- Accélération dramatique dans les dernières secondes
            local final_progress = (progress - 0.7) / 0.3
            blackout_alpha = 255 * (0.7 + final_progress * final_progress * 0.3)
        end
    else
        -- Noir complet
        blackout_alpha = 255
    end

    -- Dessiner l'overlay noir
    surface.SetDrawColor(0, 0, 0, blackout_alpha)
    surface.DrawRect(0, 0, ScrW(), ScrH())

    -- Effet de vignette pour renforcer l'obscurité
    if blackout_alpha > 100 then
        local center_x, center_y = ScrW() / 2, ScrH() / 2
        local max_radius = math.max(ScrW(), ScrH())

        for i = 1, 10 do
            local radius = (i / 10) * max_radius
            local vignette_alpha = math.min(blackout_alpha, (i / 10) * blackout_alpha * 0.3)

            -- Créer un effet de vignette circulaire
            surface.SetDrawColor(0, 0, 0, vignette_alpha)
            surface.DrawOutlinedRect(center_x - radius, center_y - radius, radius * 2, radius * 2)
        end
    end
end)

hook.Add("RenderScreenspaceEffects", "CorruptionEffects", function()
    if corrupted then
        local tab = {
            ["$pp_colour_addr"] = 0.1,
            ["$pp_colour_addg"] = 0,
            ["$pp_colour_addb"] = 0,
            ["$pp_colour_brightness"] = -0.1,
            ["$pp_colour_contrast"] = 1.1,
            ["$pp_colour_colour"] = 0.8,
            ["$pp_colour_mulr"] = 1,
            ["$pp_colour_mulg"] = 1,
            ["$pp_colour_mulb"] = 1
        }
        DrawColorModify(tab)
    end
end)
