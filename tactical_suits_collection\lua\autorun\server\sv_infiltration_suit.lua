-- Infiltration Mk IV Suit - Server Side
-- Combinaison de camouflage optique

-- Configuration
INFILTRATION_SUIT = INFILTRATION_SUIT or {}
INFILTRATION_SUIT.Config = {
    InvisibilityDelay = 3, -- <PERSON><PERSON><PERSON> avant invisibilité complète (secondes)
    MovementThreshold = 50, -- Seuil de mouvement pour perdre l'invisibilité
    PartialAlpha = 50, -- Transparence partielle en mouvement
}

-- Table des joueurs équipés
INFILTRATION_SUIT.EquippedPlayers = INFILTRATION_SUIT.EquippedPlayers or {}

-- Fonction pour équiper la combinaison
function INFILTRATION_SUIT:EquipPlayer(ply)
    if not IsValid(ply) then return false end
    
    -- Vérifier si le joueur porte déjà une autre combinaison
    if UTS_SUIT and UTS_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange de combinaison : UTS MkVII → Infiltration Mk IV")
        UTS_SUIT:UnequipPlayer(ply)
    elseif COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange de combinaison : Combat Mk II → Infiltration Mk IV")
        COMBAT_SUIT:UnequipPlayer(ply)
    elseif HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange d'équipement : Armure Lourde Mk III → Infiltration Mk IV")
        HEAVY_ARMOR:UnequipPlayer(ply)
    end
    
    -- Vérifier si le joueur porte déjà cette combinaison
    if self.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Vous portez déjà l'Infiltration Mk IV!")
        return false
    end
    
    -- Équiper la combinaison
    self.EquippedPlayers[ply:SteamID()] = {
        lastMovement = CurTime(),
        isInvisible = false,
        lastPosition = ply:GetPos()
    }
    
    -- Envoyer au client
    net.Start("INFILTRATION_Equip")
    net.Send(ply)
    
    ply:ChatPrint("Infiltration Mk IV activé - Systèmes de camouflage opérationnels")
    
    return true
end

-- Fonction pour déséquiper la combinaison
function INFILTRATION_SUIT:UnequipPlayer(ply)
    if not IsValid(ply) then return false end
    
    if not self.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Vous ne portez pas l'Infiltration Mk IV!")
        return false
    end
    
    -- Restaurer la visibilité
    ply:SetColor(Color(255, 255, 255, 255))
    ply:SetRenderMode(RENDERMODE_NORMAL)
    
    -- Retirer de la liste
    self.EquippedPlayers[ply:SteamID()] = nil
    
    -- Envoyer au client
    net.Start("INFILTRATION_Unequip")
    net.Send(ply)
    
    ply:ChatPrint("Infiltration Mk IV désactivé")
    
    return true
end

-- Hook pour gérer le camouflage
hook.Add("Think", "INFILTRATION_CamoSystem", function()
    for steamID, suitData in pairs(INFILTRATION_SUIT.EquippedPlayers) do
        local ply = player.GetBySteamID(steamID)
        if IsValid(ply) then
            local currentPos = ply:GetPos()
            local movement = currentPos:Distance(suitData.lastPosition)
            
            -- Détecter le mouvement
            if movement > INFILTRATION_SUIT.Config.MovementThreshold then
                suitData.lastMovement = CurTime()
                suitData.lastPosition = currentPos
                
                -- Transparence partielle en mouvement
                if suitData.isInvisible then
                    ply:SetColor(Color(255, 255, 255, INFILTRATION_SUIT.Config.PartialAlpha))
                    ply:SetRenderMode(RENDERMODE_TRANSALPHA)
                    suitData.isInvisible = false
                    
                    -- Envoyer au client
                    net.Start("INFILTRATION_CamoUpdate")
                    net.WriteString("partial")
                    net.Send(ply)
                end
            else
                -- Immobile - devenir invisible après délai
                if not suitData.isInvisible and CurTime() - suitData.lastMovement > INFILTRATION_SUIT.Config.InvisibilityDelay then
                    ply:SetColor(Color(255, 255, 255, 0))
                    ply:SetRenderMode(RENDERMODE_TRANSALPHA)
                    suitData.isInvisible = true
                    
                    -- Envoyer au client
                    net.Start("INFILTRATION_CamoUpdate")
                    net.WriteString("invisible")
                    net.Send(ply)
                end
            end
        end
    end
end)

-- Nettoyage à la déconnexion
hook.Add("PlayerDisconnected", "INFILTRATION_Cleanup", function(ply)
    if INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()] then
        INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()] = nil
    end
end)

-- Nettoyage à la mort
hook.Add("PlayerDeath", "INFILTRATION_Death", function(victim, inflictor, attacker)
    if INFILTRATION_SUIT.EquippedPlayers[victim:SteamID()] then
        INFILTRATION_SUIT:UnequipPlayer(victim)
    end
end)

-- Netmessages
util.AddNetworkString("INFILTRATION_Equip")
util.AddNetworkString("INFILTRATION_Unequip")
util.AddNetworkString("INFILTRATION_CamoUpdate")
util.AddNetworkString("INFILTRATION_RequestEquip")
util.AddNetworkString("INFILTRATION_RequestUnequip")

-- Recevoir les demandes d'équipement
net.Receive("INFILTRATION_RequestEquip", function(len, ply)
    if not IsValid(ply) then return end
    
    -- Vérifier si le joueur porte une autre combinaison
    if (UTS_SUIT and UTS_SUIT.EquippedPlayers[ply:SteamID()]) or
       (COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[ply:SteamID()]) or
       (HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[ply:SteamID()]) then
        ply:ChatPrint("Vous portez déjà un équipement. Utilisez !infiltration unequip d'abord ou utilisez un spawner pour échanger.")
        return
    end
    
    INFILTRATION_SUIT:EquipPlayer(ply)
end)

net.Receive("INFILTRATION_RequestUnequip", function(len, ply)
    INFILTRATION_SUIT:UnequipPlayer(ply)
end)

print("Infiltration Mk IV Suit - Server loaded")
