-- Tactical Suits Collection - Shared Commands
-- Système de commandes unifié pour toutes les combinaisons

-- Netmessages côté client
if CLIENT then
    local netMessages = {
        "UTS_Equip", "UTS_Unequip", "UTS_ScanResults",
        "INFILTRATION_Equip", "INFILTRATION_Unequip", "INFILTRATION_CamoUpdate",
        "COMBAT_Equip", "COMBAT_Unequip", "COMBAT_CamoUpdate",
        "HEAVY_ARMOR_Equip", "HEAVY_ARMOR_Unequip", "HEAVY_ARMOR_UpdateHealth",
        "UTS_RequestEquip", "UTS_RequestUnequip",
        "INFILTRATION_RequestEquip", "INFILTRATION_RequestUnequip",
        "COMBAT_RequestEquip", "COMBAT_RequestUnequip",
        "HEAVY_ARMOR_RequestEquip", "HEAVY_ARMOR_RequestUnequip"
    }
    
    for _, msg in ipairs(netMessages) do
        if not util.NetworkStringToID(msg) then
            print("Warning: NetMessage " .. msg .. " not found on client")
        end
    end
end

-- Commandes côté serveur
if SERVER then
    -- Commandes chat pour toutes les combinaisons
    hook.Add("PlayerSay", "TACTICAL_SUITS_ChatCommands", function(ply, text, team)
        if not IsValid(ply) then return end
        
        local command = string.lower(string.Trim(text))
        
        -- Commandes UTS MkVII
        if command == "!uts equip" then
            if UTS_SUIT then UTS_SUIT:EquipPlayer(ply) end
            return ""
        elseif command == "!uts unequip" then
            if UTS_SUIT then UTS_SUIT:UnequipPlayer(ply) end
            return ""
            
        -- Commandes Infiltration Mk IV
        elseif command == "!infiltration equip" or command == "!stealth equip" then
            if INFILTRATION_SUIT then INFILTRATION_SUIT:EquipPlayer(ply) end
            return ""
        elseif command == "!infiltration unequip" or command == "!stealth unequip" then
            if INFILTRATION_SUIT then INFILTRATION_SUIT:UnequipPlayer(ply) end
            return ""
            
        -- Commandes Combat Mk II
        elseif command == "!combat equip" then
            if COMBAT_SUIT then COMBAT_SUIT:EquipPlayer(ply) end
            return ""
        elseif command == "!combat unequip" then
            if COMBAT_SUIT then COMBAT_SUIT:UnequipPlayer(ply) end
            return ""
            
        -- Commandes Armure Lourde Mk III
        elseif command == "!heavy equip" or command == "!armor equip" then
            if HEAVY_ARMOR then HEAVY_ARMOR:EquipPlayer(ply) end
            return ""
        elseif command == "!heavy unequip" or command == "!armor unequip" then
            if HEAVY_ARMOR then HEAVY_ARMOR:UnequipPlayer(ply) end
            return ""
            
        -- Aide générale
        elseif command == "!suits help" or command == "!tactical help" then
            ply:ChatPrint("=== Collection Combinaisons Tactiques ===")
            ply:ChatPrint("!uts equip/unequip - UTS MkVII (Scan terrain)")
            ply:ChatPrint("!infiltration equip/unequip - Infiltration Mk IV (Camouflage)")
            ply:ChatPrint("!combat equip/unequip - Combat Mk II (Hybride)")
            ply:ChatPrint("!heavy equip/unequip - Armure Lourde Mk III (Tank)")
            ply:ChatPrint("!suits status - Voir votre équipement actuel")
            return ""
            
        -- Statut actuel
        elseif command == "!suits status" or command == "!tactical status" then
            local equipped = "Aucun équipement"
            
            if UTS_SUIT and UTS_SUIT.EquippedPlayers[ply:SteamID()] then
                equipped = "UTS MkVII (Scan terrain)"
            elseif INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()] then
                equipped = "Infiltration Mk IV (Camouflage)"
            elseif COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[ply:SteamID()] then
                equipped = "Combat Mk II (Hybride)"
            elseif HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[ply:SteamID()] then
                equipped = "Armure Lourde Mk III (Tank)"
            end
            
            ply:ChatPrint("Équipement actuel: " .. equipped)
            return ""
        end
    end)
    
    -- Commandes administrateur
    concommand.Add("tactical_equip", function(ply, cmd, args)
        if not IsValid(ply) or not ply:IsAdmin() then
            if IsValid(ply) then ply:ChatPrint("Admin uniquement!") end
            return
        end
        
        if not args[1] then
            ply:ChatPrint("Usage: tactical_equip <type> [playerID]")
            ply:ChatPrint("Types: uts, infiltration, combat, heavy")
            return
        end
        
        local target = ply
        if args[2] then
            local targetID = tonumber(args[2])
            if targetID then
                target = Player(targetID)
                if not IsValid(target) then
                    ply:ChatPrint("Joueur introuvable: " .. args[2])
                    return
                end
            end
        end
        
        local suitType = string.lower(args[1])
        local success = false
        
        if suitType == "uts" and UTS_SUIT then
            success = UTS_SUIT:EquipPlayer(target)
        elseif suitType == "infiltration" and INFILTRATION_SUIT then
            success = INFILTRATION_SUIT:EquipPlayer(target)
        elseif suitType == "combat" and COMBAT_SUIT then
            success = COMBAT_SUIT:EquipPlayer(target)
        elseif suitType == "heavy" and HEAVY_ARMOR then
            success = HEAVY_ARMOR:EquipPlayer(target)
        else
            ply:ChatPrint("Type invalide: " .. suitType)
            return
        end
        
        if success then
            ply:ChatPrint("Équipement " .. suitType .. " donné à " .. target:Name())
        else
            ply:ChatPrint("Échec de l'équipement pour " .. target:Name())
        end
    end)
    
    concommand.Add("tactical_unequip_all", function(ply, cmd, args)
        if not IsValid(ply) or not ply:IsAdmin() then
            if IsValid(ply) then ply:ChatPrint("Admin uniquement!") end
            return
        end
        
        local target = ply
        if args[1] then
            local targetID = tonumber(args[1])
            if targetID then
                target = Player(targetID)
                if not IsValid(target) then
                    ply:ChatPrint("Joueur introuvable: " .. args[1])
                    return
                end
            end
        end
        
        local removed = 0
        
        if UTS_SUIT and UTS_SUIT.EquippedPlayers[target:SteamID()] then
            UTS_SUIT:UnequipPlayer(target)
            removed = removed + 1
        end
        if INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[target:SteamID()] then
            INFILTRATION_SUIT:UnequipPlayer(target)
            removed = removed + 1
        end
        if COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[target:SteamID()] then
            COMBAT_SUIT:UnequipPlayer(target)
            removed = removed + 1
        end
        if HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[target:SteamID()] then
            HEAVY_ARMOR:UnequipPlayer(target)
            removed = removed + 1
        end
        
        ply:ChatPrint("Retiré " .. removed .. " équipement(s) de " .. target:Name())
    end)
    
    concommand.Add("tactical_list", function(ply, cmd, args)
        if not IsValid(ply) or not ply:IsAdmin() then
            if IsValid(ply) then ply:ChatPrint("Admin uniquement!") end
            return
        end
        
        ply:ChatPrint("=== Joueurs Équipés ===")
        
        local totalEquipped = 0
        
        for _, p in pairs(player.GetAll()) do
            local equipment = {}
            
            if UTS_SUIT and UTS_SUIT.EquippedPlayers[p:SteamID()] then
                table.insert(equipment, "UTS")
            end
            if INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[p:SteamID()] then
                table.insert(equipment, "INFILTRATION")
            end
            if COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[p:SteamID()] then
                table.insert(equipment, "COMBAT")
            end
            if HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[p:SteamID()] then
                table.insert(equipment, "HEAVY")
            end
            
            if #equipment > 0 then
                ply:ChatPrint(p:Name() .. ": " .. table.concat(equipment, ", "))
                totalEquipped = totalEquipped + 1
            end
        end
        
        if totalEquipped == 0 then
            ply:ChatPrint("Aucun joueur équipé actuellement.")
        else
            ply:ChatPrint("Total: " .. totalEquipped .. " joueur(s) équipé(s)")
        end
    end)
    
    concommand.Add("tactical_spawn_all_spawners", function(ply, cmd, args)
        if not IsValid(ply) or not ply:IsAdmin() then
            if IsValid(ply) then ply:ChatPrint("Admin uniquement!") end
            return
        end
        
        local trace = ply:GetEyeTrace()
        local basePos = trace.HitPos + trace.HitNormal * 10
        local ang = ply:EyeAngles()
        ang.p = 0
        
        local spawners = {
            {class = "uts_spawner", offset = Vector(0, 0, 0)},
            {class = "infiltration_spawner", offset = Vector(100, 0, 0)},
            {class = "combat_spawner", offset = Vector(200, 0, 0)},
            {class = "heavy_armor_spawner", offset = Vector(300, 0, 0)}
        }
        
        local created = 0
        for _, spawnerData in ipairs(spawners) do
            local spawner = ents.Create(spawnerData.class)
            if IsValid(spawner) then
                spawner:SetPos(basePos + spawnerData.offset)
                spawner:SetAngles(ang)
                spawner:Spawn()
                spawner:Activate()
                created = created + 1
            end
        end
        
        ply:ChatPrint("Créé " .. created .. " spawner(s) de combinaisons tactiques!")
    end)
end

print("Tactical Suits Collection - Commands loaded")
