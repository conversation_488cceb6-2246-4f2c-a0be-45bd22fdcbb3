-- UTS MkVII Suit - Server Side
-- Combinaison tactique avec scan de terrain

-- Configuration
UTS_SUIT = UTS_SUIT or {}
UTS_SUIT.Config = {
    ScanRadius = 1000, -- Rayon de scan en unités
    ScanInterval = 2, -- Intervalle entre les scans en secondes
    ArmorAmount = 100, -- Points d'armure
}

-- Table des joueurs équipés
UTS_SUIT.EquippedPlayers = UTS_SUIT.EquippedPlayers or {}

-- Fonction pour équiper la combinaison
function UTS_SUIT:EquipPlayer(ply)
    if not IsValid(ply) then return false end
    
    -- Vérifier si le joueur porte déjà une autre combinaison
    if INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:Chat<PERSON>rint("Échange de combinaison : Infiltration Mk IV → UTS MkVII")
        INFILTRATION_SUIT:UnequipPlayer(ply)
    elseif COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange de combinaison : Combat Mk II → UTS MkVII")
        COMBAT_SUIT:UnequipPlayer(ply)
    elseif HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange d'équipement : Armure Lourde Mk III → UTS MkVII")
        HEAVY_ARMOR:UnequipPlayer(ply)
    end
    
    -- Vérifier si le joueur porte déjà cette combinaison
    if self.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Vous portez déjà l'UTS MkVII!")
        return false
    end
    
    -- Équiper la combinaison
    self.EquippedPlayers[ply:SteamID()] = {
        lastScan = 0,
        originalArmor = ply:Armor()
    }
    
    ply:SetArmor(self.Config.ArmorAmount)
    
    -- Envoyer au client
    net.Start("UTS_Equip")
    net.Send(ply)
    
    ply:ChatPrint("UTS MkVII activé - Systèmes de scan opérationnels")
    
    return true
end

-- Fonction pour déséquiper la combinaison
function UTS_SUIT:UnequipPlayer(ply)
    if not IsValid(ply) then return false end
    
    local suitData = self.EquippedPlayers[ply:SteamID()]
    if not suitData then
        ply:ChatPrint("Vous ne portez pas l'UTS MkVII!")
        return false
    end
    
    -- Restaurer l'armure originale
    ply:SetArmor(suitData.originalArmor)
    
    -- Retirer de la liste
    self.EquippedPlayers[ply:SteamID()] = nil
    
    -- Envoyer au client
    net.Start("UTS_Unequip")
    net.Send(ply)
    
    ply:ChatPrint("UTS MkVII désactivé")
    
    return true
end

-- Fonction de scan
function UTS_SUIT:PerformScan(ply)
    if not IsValid(ply) then return end
    
    local suitData = self.EquippedPlayers[ply:SteamID()]
    if not suitData then return end
    
    -- Vérifier l'intervalle de scan
    if CurTime() - suitData.lastScan < self.Config.ScanInterval then return end
    suitData.lastScan = CurTime()
    
    local scanResults = {}
    local playerPos = ply:GetPos()
    
    -- Scanner les entités dans le rayon
    for _, ent in pairs(ents.FindInSphere(playerPos, self.Config.ScanRadius)) do
        if IsValid(ent) and ent ~= ply then
            local entType = "unknown"
            local entName = "Entité"
            
            if ent:IsPlayer() then
                entType = "player"
                entName = ent:Name()
            elseif ent:IsNPC() then
                entType = "npc"
                entName = string.gsub(ent:GetClass(), "npc_", "")
            elseif ent:IsWeapon() then
                entType = "weapon"
                entName = ent:GetPrintName() or ent:GetClass()
            elseif string.find(ent:GetClass(), "prop_") then
                entType = "prop"
                entName = ent:GetModel() and string.GetFileFromFilename(ent:GetModel()) or "Prop"
            end
            
            table.insert(scanResults, {
                pos = ent:GetPos(),
                type = entType,
                name = entName,
                distance = math.Round(playerPos:Distance(ent:GetPos()))
            })
        end
    end
    
    -- Envoyer les résultats au client
    net.Start("UTS_ScanResults")
    net.WriteTable(scanResults)
    net.Send(ply)
end

-- Hook pour le scan automatique
hook.Add("Think", "UTS_AutoScan", function()
    for steamID, suitData in pairs(UTS_SUIT.EquippedPlayers) do
        local ply = player.GetBySteamID(steamID)
        if IsValid(ply) then
            UTS_SUIT:PerformScan(ply)
        end
    end
end)

-- Nettoyage à la déconnexion
hook.Add("PlayerDisconnected", "UTS_Cleanup", function(ply)
    if UTS_SUIT.EquippedPlayers[ply:SteamID()] then
        UTS_SUIT.EquippedPlayers[ply:SteamID()] = nil
    end
end)

-- Nettoyage à la mort
hook.Add("PlayerDeath", "UTS_Death", function(victim, inflictor, attacker)
    if UTS_SUIT.EquippedPlayers[victim:SteamID()] then
        UTS_SUIT:UnequipPlayer(victim)
    end
end)

-- Netmessages
util.AddNetworkString("UTS_Equip")
util.AddNetworkString("UTS_Unequip")
util.AddNetworkString("UTS_ScanResults")
util.AddNetworkString("UTS_RequestEquip")
util.AddNetworkString("UTS_RequestUnequip")

-- Recevoir les demandes d'équipement
net.Receive("UTS_RequestEquip", function(len, ply)
    if not IsValid(ply) then return end
    
    -- Vérifier si le joueur porte une autre combinaison
    if (INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()]) or
       (COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[ply:SteamID()]) or
       (HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[ply:SteamID()]) then
        ply:ChatPrint("Vous portez déjà un équipement. Utilisez !uts unequip d'abord ou utilisez un spawner pour échanger.")
        return
    end
    
    UTS_SUIT:EquipPlayer(ply)
end)

net.Receive("UTS_RequestUnequip", function(len, ply)
    UTS_SUIT:UnequipPlayer(ply)
end)

print("UTS MkVII Suit - Server loaded")
