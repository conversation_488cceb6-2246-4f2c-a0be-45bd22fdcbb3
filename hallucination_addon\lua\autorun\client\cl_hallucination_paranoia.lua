-- Hallucination Paranoia Effects
-- Effets de paranoïa et de faux joueurs

local fakePlayerModels = {
    "models/player/group01/female_01.mdl",
    "models/player/group01/female_02.mdl", 
    "models/player/group01/male_01.mdl",
    "models/player/group01/male_02.mdl",
    "models/player/group01/male_03.mdl",
    "models/player/group01/male_04.mdl",
    "models/player/group01/male_05.mdl",
    "models/player/group01/male_06.mdl",
    "models/player/group01/male_07.mdl",
    "models/player/group01/male_08.mdl",
    "models/player/group01/male_09.mdl"
}

local fakePlayers = {}
local paranoiaMessages = {
    "Quelqu'un vous observe...",
    "Vous entendez des pas derrière vous...",
    "Une ombre bouge dans votre vision périphérique...",
    "Vous n'êtes pas seul...",
    "Ils savent que vous êtes là...",
    "Quelque chose ne va pas...",
    "Vous sentez une présence...",
    "Regardez derrière vous...",
    "Ils arrivent...",
    "Vous ne pouvez pas leur échapper..."
}

-- Fonction pour créer un faux joueur
local function CreateFakePlayer(pos, name)
    local fakePlayer = {
        pos = pos or (LocalPlayer():GetPos() + Vector(math.random(-300, 300), math.random(-300, 300), 0)),
        name = name or "Inconnu",
        model = fakePlayerModels[math.random(1, #fakePlayerModels)],
        angles = Angle(0, math.random(0, 360), 0),
        alpha = 255,
        created = CurTime(),
        lastMove = CurTime(),
        targetPos = nil,
        isMoving = false,
        animationFrame = 0
    }
    
    -- Définir une position cible aléatoire
    fakePlayer.targetPos = fakePlayer.pos + Vector(math.random(-200, 200), math.random(-200, 200), 0)
    
    table.insert(fakePlayers, fakePlayer)
    return fakePlayer
end

-- Fonction pour mettre à jour les faux joueurs
local function UpdateFakePlayers()
    for i, fakePlayer in ipairs(fakePlayers) do
        local timeSinceCreated = CurTime() - fakePlayer.created
        
        -- Supprimer après 15 secondes
        if timeSinceCreated > 15 then
            table.remove(fakePlayers, i)
            continue
        end
        
        -- Faire disparaître progressivement
        if timeSinceCreated > 10 then
            fakePlayer.alpha = math.max(0, 255 - ((timeSinceCreated - 10) / 5) * 255)
        end
        
        -- Mouvement vers la position cible
        if fakePlayer.targetPos then
            local distance = fakePlayer.pos:Distance(fakePlayer.targetPos)
            if distance > 10 then
                local direction = (fakePlayer.targetPos - fakePlayer.pos):GetNormalized()
                fakePlayer.pos = fakePlayer.pos + direction * 50 * FrameTime()
                fakePlayer.angles.y = math.deg(math.atan2(direction.y, direction.x))
                fakePlayer.isMoving = true
                fakePlayer.animationFrame = fakePlayer.animationFrame + FrameTime() * 10
            else
                fakePlayer.isMoving = false
                -- Nouvelle position cible
                if CurTime() - fakePlayer.lastMove > 3 then
                    fakePlayer.targetPos = fakePlayer.pos + Vector(math.random(-200, 200), math.random(-200, 200), 0)
                    fakePlayer.lastMove = CurTime()
                end
            end
        end
    end
end

-- Fonction pour dessiner les faux joueurs
local function DrawFakePlayers()
    for _, fakePlayer in ipairs(fakePlayers) do
        -- Vérifier si le faux joueur est dans le champ de vision
        local plyPos = LocalPlayer():EyePos()
        local plyAng = LocalPlayer():EyeAngles()
        local toFakePlayer = (fakePlayer.pos - plyPos):GetNormalized()
        local dot = plyAng:Forward():Dot(toFakePlayer)
        
        -- Ne dessiner que si dans le champ de vision (environ 90 degrés)
        if dot > 0.3 then
            -- Calculer la distance pour l'effet de brouillard
            local distance = plyPos:Distance(fakePlayer.pos)
            local fogAlpha = math.max(0, 1 - (distance / 500))
            
            -- Matrice de transformation
            local matrix = Matrix()
            matrix:Translate(fakePlayer.pos)
            matrix:Rotate(fakePlayer.angles)
            
            -- Couleur avec transparence
            render.SetColorModulation(0.8, 0.8, 1) -- Teinte bleuâtre
            render.SetBlend(fakePlayer.alpha / 255 * fogAlpha)
            
            -- Matériau fantomatique
            render.ModelMaterialOverride(Material("models/wireframe"))
            render.SetModelMatrix(matrix)
            
            -- Dessiner le modèle
            local model = Model(fakePlayer.model)
            if model then
                model:DrawModel()
            end
            
            render.SetModelMatrix()
            render.ModelMaterialOverride()
            render.SetColorModulation(1, 1, 1)
            render.SetBlend(1)
            
            -- Dessiner le nom au-dessus
            if distance < 200 then
                local screenPos = fakePlayer.pos:ToScreen()
                if screenPos.visible then
                    draw.SimpleText(fakePlayer.name, "DermaDefault", screenPos.x, screenPos.y - 20, 
                        Color(255, 255, 255, fakePlayer.alpha * fogAlpha), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                end
            end
        end
    end
end

-- Fonction pour afficher des messages de paranoïa
local function ShowParanoiaMessage()
    local message = paranoiaMessages[math.random(1, #paranoiaMessages)]
    LocalPlayer():PrintMessage(HUD_PRINTCENTER, message)
    
    -- Effet sonore subtil
    surface.PlaySound("ambient/wind/wind_snippet5.wav")
end

-- Hook pour les effets de paranoïa
hook.Add("Think", "HallucinationParanoiaEffects", function()
    if not isHallucinating then return end
    
    if currentHallucinationType == HALLUCINATION_TYPES.FAKE_PLAYERS or 
       currentHallucinationType == HALLUCINATION_TYPES.PARANOIA_MODE then
        
        UpdateFakePlayers()
        
        -- Créer occasionnellement de nouveaux faux joueurs
        if math.random() < 0.0005 and #fakePlayers < 3 then -- 0.05% de chance par frame
            CreateFakePlayer()
        end
        
        -- Afficher des messages de paranoïa
        if currentHallucinationType == HALLUCINATION_TYPES.PARANOIA_MODE and math.random() < 0.0002 then
            ShowParanoiaMessage()
        end
    end
end)

-- Hook pour dessiner les faux joueurs
hook.Add("PostDrawOpaqueRenderables", "HallucinationFakePlayers", function()
    if isHallucinating and (currentHallucinationType == HALLUCINATION_TYPES.FAKE_PLAYERS or 
                           currentHallucinationType == HALLUCINATION_TYPES.PARANOIA_MODE) then
        DrawFakePlayers()
    end
end)

-- Nettoyer les faux joueurs quand l'hallucination s'arrête
hook.Add("HallucinationStopped", "CleanupFakePlayers", function()
    fakePlayers = {}
end)
