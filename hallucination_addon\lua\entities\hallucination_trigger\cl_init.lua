include("shared.lua")

function ENT:Initialize()
    -- Effet de particules autour du trigger
    self.NextParticleTime = 0
end

function ENT:Draw()
    self:DrawModel()
    
    -- Effet de particules magiques
    if CurTime() > self.NextParticleTime then
        local pos = self:GetPos()
        local effectdata = EffectData()
        effectdata:SetOrigin(pos)
        effectdata:SetMagnitude(1)
        effectdata:SetScale(1)
        util.Effect("ManhackSparks", effectdata)
        
        self.NextParticleTime = CurTime() + 0.5
    end
end

function ENT:Think()
    -- Faire flotter l'entité légèrement
    local pos = self:GetPos()
    local newPos = pos + Vector(0, 0, math.sin(CurTime() * 2) * 2)
    self:SetPos(newPos)
    
    -- Rotation lente
    local ang = self:GetAngles()
    ang.y = ang.y + 1
    self:SetAngles(ang)
    
    self:NextThink(CurTime() + 0.01)
    return true
end
