-- Infiltration Spawner Entity - Server Side

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    self:SetModel("models/props_lab/huladoll.mdl")
    self:SetModelScale(1.2, 0)
    
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetUseType(SIMPLE_USE)
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:EnableMotion(false)
    end
    
    -- Couleur grise pour l'Infiltration
    self:SetColor(Color(100, 100, 120, 255))
    
    self.LastUseTime = 0
    self.UseDelay = 1
end

function ENT:Use(activator, caller)
    if not IsValid(activator) or not activator:IsPlayer() then return end
    
    if CurTime() - self.LastUseTime < self.UseDelay then return end
    self.LastUseTime = CurTime()
    
    if not INFILTRATION_SUIT then
        activator:ChatPrint("Erreur: Système Infiltration non initialisé!")
        return
    end
    
    -- Gestion de l'échange
    if activator.infiltration_exchange_ready then
        activator.infiltration_exchange_ready = nil
        INFILTRATION_SUIT:EquipPlayer(activator)
        return
    end
    
    -- Vérifier les autres combinaisons
    if UTS_SUIT and UTS_SUIT.EquippedPlayers[activator:SteamID()] then
        activator:ChatPrint("Échanger l'UTS MkVII contre l'Infiltration Mk IV ? Utilisez à nouveau pour confirmer.")
        activator.infiltration_exchange_ready = true
        timer.Simple(5, function() if IsValid(activator) then activator.infiltration_exchange_ready = nil end end)
        return
    elseif COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[activator:SteamID()] then
        activator:ChatPrint("Échanger le Combat Mk II contre l'Infiltration Mk IV ? Utilisez à nouveau pour confirmer.")
        activator.infiltration_exchange_ready = true
        timer.Simple(5, function() if IsValid(activator) then activator.infiltration_exchange_ready = nil end end)
        return
    elseif HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[activator:SteamID()] then
        activator:ChatPrint("Échanger l'Armure Lourde Mk III contre l'Infiltration Mk IV ? Utilisez à nouveau pour confirmer.")
        activator.infiltration_exchange_ready = true
        timer.Simple(5, function() if IsValid(activator) then activator.infiltration_exchange_ready = nil end end)
        return
    elseif INFILTRATION_SUIT.EquippedPlayers[activator:SteamID()] then
        INFILTRATION_SUIT:UnequipPlayer(activator)
        return
    end
    
    INFILTRATION_SUIT:EquipPlayer(activator)
end

function ENT:Think()
    local ang = self:GetAngles()
    ang.y = ang.y + 0.5
    self:SetAngles(ang)
    
    self:NextThink(CurTime() + 0.1)
    return true
end
