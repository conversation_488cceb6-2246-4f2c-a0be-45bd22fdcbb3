-- Heavy Combat Armor Mk III - Client Side
-- Interface HUD pour l'armure de combat lourde avec vision tactique

-- Configuration client
HEAVY_ARMOR_CLIENT = HEAVY_ARMOR_CLIENT or {}
HEAVY_ARMOR_CLIENT.Active = false
HEAVY_ARMOR_CLIENT.ArmorHealth = 300
HEAVY_ARMOR_CLIENT.MaxArmorHealth = 300

-- Couleurs de l'interface (thème orange/noir)
local COLOR_BG = Color(50, 25, 0, 200)
local COLOR_BORDER = Color(255, 150, 0, 255)
local COLOR_TEXT = Color(255, 255, 255, 255)
local COLOR_ARMOR = Color(255, 150, 0, 255)
local COLOR_WARNING = Color(255, 50, 50, 255)

-- Fonction pour dessiner l'HUD de l'armure lourde
function HEAVY_ARMOR_CLIENT:DrawHUD()
    if not self.Active then return end
    
    local scrW, scrH = ScrW(), ScrH()
    
    -- Panneau principal (position centre-haut)
    local panelW, panelH = 350, 155
    local x, y = scrW/2 - panelW/2, 20
    
    -- Fond du panneau
    draw.RoundedBox(8, x, y, panelW, panelH, COLOR_BG)
    draw.RoundedBox(8, x, y, panelW, 35, COLOR_BORDER)
    
    -- Titre
    draw.SimpleText("ARMURE LOURDE MK III", "DermaLarge", x + panelW/2, y + 17, COLOR_TEXT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Ligne de séparation
    draw.RoundedBox(0, x + 10, y + 40, panelW - 20, 2, COLOR_BORDER)
    
    -- Intégrité de l'armure
    local armorPercent = self.ArmorHealth / self.MaxArmorHealth * 100
    draw.SimpleText("INTÉGRITÉ BLINDAGE:", "DermaDefault", x + 15, y + 50, COLOR_TEXT)
    draw.SimpleText(self.ArmorHealth .. "/" .. self.MaxArmorHealth .. " (" .. math.Round(armorPercent) .. "%)", "DermaDefault", x + 15, y + 70, COLOR_ARMOR)
    
    -- Barre d'intégrité
    local armorBarW = panelW - 30
    local armorBarH = 15
    local armorBarX = x + 15
    local armorBarY = y + 85
    
    draw.RoundedBox(2, armorBarX, armorBarY, armorBarW, armorBarH, Color(0, 0, 0, 150))
    local armorFillW = armorBarW * (armorPercent / 100)
    local armorColor = armorPercent > 50 and COLOR_ARMOR or COLOR_WARNING
    draw.RoundedBox(2, armorBarX, armorBarY, armorFillW, armorBarH, armorColor)
    
    -- Statut des systèmes
    local statusText = "PROTECTION TOTALE ACTIVE"
    local statusColor = COLOR_ARMOR
    
    if armorPercent < 25 then
        statusText = "PROTECTION CRITIQUE"
        statusColor = COLOR_WARNING
    elseif armorPercent < 50 then
        statusText = "PROTECTION ENDOMMAGÉE"
        statusColor = Color(255, 200, 0)
    elseif armorPercent > 75 then
        statusText = "PROTECTION MAXIMALE"
        statusColor = Color(100, 255, 100)
    end
    
    draw.SimpleText(statusText, "DermaDefault", x + panelW/2, y + 110, statusColor, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Indicateur de vision tactique
    local visionText = "VISION TACTIQUE: ACTIVE"
    local visionColor = Color(100, 255, 255, 255)
    draw.SimpleText(visionText, "DermaDefault", x + panelW/2, y + 125, visionColor, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Panneau d'informations (côté droit)
    local infoPanelW, infoPanelH = 200, 100
    local infoX, infoY = scrW - infoPanelW - 20, 20
    
    draw.RoundedBox(8, infoX, infoY, infoPanelW, infoPanelH, COLOR_BG)
    draw.RoundedBox(8, infoX, infoY, infoPanelW, 25, COLOR_BORDER)
    
    draw.SimpleText("CARACTÉRISTIQUES", "DermaDefault", infoX + infoPanelW/2, infoY + 12, COLOR_TEXT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Informations sur l'armure
    local specs = {
        "Protection: TOTALE",
        "Vision: Tactique",
        "Vitesse: -30%",
        "Saut: +50%"
    }
    
    for i, spec in ipairs(specs) do
        draw.SimpleText(spec, "DermaDefault", infoX + 10, infoY + 25 + (i * 15), COLOR_TEXT)
    end
    
    -- Avertissements critiques
    if armorPercent < 15 then
        local warningText = "ALERTE: PROTECTION DÉFAILLANTE"
        local pulse = math.sin(CurTime() * 8) * 0.5 + 0.5
        local warningColor = Color(255 * pulse, 50, 50, 255)
        draw.SimpleText(warningText, "DermaLarge", scrW/2, scrH - 100, warningColor, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        draw.SimpleText("PV VULNÉRABLES DANS " .. math.ceil(armorPercent/100 * 300) .. " DÉGÂTS", "DermaDefault", scrW/2, scrH - 70, warningColor, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
end

-- Fonction pour dessiner la vision tactique
function HEAVY_ARMOR_CLIENT:DrawTacticalVision()
    if not self.Active then return end
    
    local ply = LocalPlayer()
    if not IsValid(ply) then return end
    
    local maxDistance = 1500 -- Distance maximale de détection
    
    -- Scanner tous les joueurs
    for _, target in pairs(player.GetAll()) do
        if target ~= ply and IsValid(target) and target:Alive() then
            local distance = ply:GetPos():Distance(target:GetPos())
            
            if distance <= maxDistance then
                local screenPos = target:GetPos():ToScreen()
                
                if screenPos.visible then
                    local color = Color(255, 100, 100, 200) -- Rouge par défaut
                    
                    if target:Team() == ply:Team() then
                        color = Color(100, 255, 100, 200) -- Vert pour alliés
                    end
                    
                    local headPos = target:GetBonePosition(target:LookupBone("ValveBiped.Bip01_Head1") or 0)
                    local headScreen = headPos:ToScreen()
                    
                    if headScreen.visible then
                        -- Cercle au-dessus de la tête
                        draw.NoTexture()
                        surface.SetDrawColor(color)
                        surface.DrawOutlinedCircle(headScreen.x, headScreen.y - 20, 15, 2)
                        
                        -- Nom et distance
                        draw.SimpleText(target:Name(), "DermaDefault", headScreen.x, headScreen.y - 45, color, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                        local dist = math.Round(distance / 52.49)
                        draw.SimpleText(dist .. "m", "DermaDefault", headScreen.x, headScreen.y - 30, color, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                    end
                    
                    -- Ligne vers le joueur si proche
                    if distance <= 500 then
                        local centerX, centerY = ScrW()/2, ScrH()/2
                        surface.SetDrawColor(color.r, color.g, color.b, 100)
                        surface.DrawLine(centerX, centerY, screenPos.x, screenPos.y)
                    end
                end
            end
        end
    end
    
    -- Scanner les NPCs
    for _, npc in pairs(ents.FindByClass("npc_*")) do
        if IsValid(npc) and npc:Health() > 0 then
            local distance = ply:GetPos():Distance(npc:GetPos())
            
            if distance <= maxDistance then
                local screenPos = npc:GetPos():ToScreen()
                
                if screenPos.visible then
                    local color = Color(255, 255, 100, 200) -- Jaune pour NPCs
                    
                    -- Carré pour les NPCs
                    draw.NoTexture()
                    surface.SetDrawColor(color)
                    surface.DrawOutlinedRect(screenPos.x - 10, screenPos.y - 10, 20, 20)
                    
                    -- Type et distance
                    local npcClass = string.gsub(npc:GetClass(), "npc_", "")
                    draw.SimpleText(string.upper(npcClass), "DermaDefault", screenPos.x, screenPos.y - 25, color, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                    local dist = math.Round(distance / 52.49)
                    draw.SimpleText(dist .. "m", "DermaDefault", screenPos.x, screenPos.y + 15, color, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                end
            end
        end
    end
end

-- Fonction pour activer l'armure
function HEAVY_ARMOR_CLIENT:Activate(health, maxHealth)
    self.Active = true
    self.ArmorHealth = health or 300
    self.MaxArmorHealth = maxHealth or 300
    
    -- Désactiver les autres HUDs
    if UTS_SUIT_CLIENT then UTS_SUIT_CLIENT.Active = false end
    if INFILTRATION_SUIT_CLIENT then INFILTRATION_SUIT_CLIENT.Active = false end
    if COMBAT_SUIT_CLIENT then COMBAT_SUIT_CLIENT.Active = false end
    
    chat.AddText(Color(255, 150, 0), "[Armure Lourde Mk III] ", Color(255, 255, 255), "Systèmes de protection activés")
end

-- Fonction pour désactiver l'armure
function HEAVY_ARMOR_CLIENT:Deactivate()
    self.Active = false
    
    chat.AddText(Color(255, 100, 100), "[Armure Lourde Mk III] ", Color(255, 255, 255), "Systèmes de protection désactivés")
end

-- Fonction pour mettre à jour la santé
function HEAVY_ARMOR_CLIENT:UpdateHealth(health)
    self.ArmorHealth = health
end

-- Hook pour dessiner le HUD
hook.Add("HUDPaint", "HEAVY_ARMOR_DrawHUD", function()
    HEAVY_ARMOR_CLIENT:DrawHUD()
    HEAVY_ARMOR_CLIENT:DrawTacticalVision()
end)

-- Désactiver automatiquement l'armure quand le joueur meurt
hook.Add("PlayerDeath", "HEAVY_ARMOR_ClientPlayerDeath", function(victim, inflictor, attacker)
    if victim == LocalPlayer() and HEAVY_ARMOR_CLIENT.Active then
        HEAVY_ARMOR_CLIENT:Deactivate()
    end
end)

-- Netmessages
net.Receive("HEAVY_ARMOR_Equip", function()
    local health = net.ReadInt(16)
    local maxHealth = net.ReadInt(16)
    HEAVY_ARMOR_CLIENT:Activate(health, maxHealth)
end)

net.Receive("HEAVY_ARMOR_Unequip", function()
    HEAVY_ARMOR_CLIENT:Deactivate()
end)

net.Receive("HEAVY_ARMOR_UpdateHealth", function()
    local health = net.ReadInt(16)
    HEAVY_ARMOR_CLIENT:UpdateHealth(health)
end)

print("Heavy Combat Armor Mk III - Client loaded")
