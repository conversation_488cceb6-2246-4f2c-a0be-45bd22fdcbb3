-- Hallucination Test Commands
-- Commandes de test pour développeurs

-- Commande pour tester tous les types d'hallucinations
concommand.Add("hallucination_test_all", function(ply, cmd, args)
    if not ply:IsAdmin() then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Vous devez être administrateur pour utiliser cette commande.")
        return
    end
    
    ply:PrintMessage(HUD_PRINTCONSOLE, "=== Test de tous les types d'hallucinations ===")
    
    -- Tester chaque type d'hallucination pendant 10 secondes
    for i = 1, 6 do
        timer.Simple((i - 1) * 12, function()
            if IsValid(ply) then
                local typeName = ""
                if i == 1 then typeName = "Distorsion Visuelle"
                elseif i == 2 then typeName = "Entités Factices"
                elseif i == 3 then typeName = "Hallucination Audio"
                elseif i == 4 then typeName = "Effets d'Écran"
                elseif i == 5 then typeName = "Faux Joueurs"
                elseif i == 6 then typeName = "Mode Paranoïa"
                end
                
                ply:PrintMessage(HUD_PRINTCONSOLE, "Test: " .. typeName)
                
                -- Démarrer l'hallucination
                local steamID = ply:SteamID()
                HallucinatingPlayers[steamID] = {
                    player = ply,
                    startTime = CurTime(),
                    duration = 10,
                    type = i,
                    intensity = 1.0
                }
                
                net.Start("StartHallucination")
                net.WriteInt(i, 8)
                net.WriteFloat(10)
                net.WriteFloat(1.0)
                net.Send(ply)
                
                -- Arrêter après 10 secondes
                timer.Simple(10, function()
                    if IsValid(ply) and HallucinatingPlayers[steamID] then
                        HallucinatingPlayers[steamID] = nil
                        net.Start("StopHallucination")
                        net.Send(ply)
                    end
                end)
            end
        end)
    end
end)

-- Commande pour spawner plusieurs triggers de test
concommand.Add("hallucination_spawn_triggers", function(ply, cmd, args)
    if not ply:IsAdmin() then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Vous devez être administrateur pour utiliser cette commande.")
        return
    end
    
    local pos = ply:GetPos()
    local ang = ply:GetAngles()
    
    -- Spawner 6 triggers, un pour chaque type
    for i = 1, 6 do
        local offset = Vector(math.cos(math.rad(i * 60)) * 200, math.sin(math.rad(i * 60)) * 200, 50)
        local triggerPos = pos + offset
        
        local trigger = ents.Create("hallucination_trigger")
        if IsValid(trigger) then
            trigger:SetPos(triggerPos)
            trigger:SetAngles(ang)
            trigger:Spawn()
            trigger:Activate()
            
            -- Configurer le type d'hallucination
            trigger:SetHallucinationType(i)
            trigger:SetHallucinationDuration(30)
            trigger:SetHallucinationIntensity(1.0)
            
            -- Changer la couleur selon le type
            local colors = {
                Color(255, 0, 0, 100),   -- Rouge pour distorsion
                Color(0, 255, 0, 100),   -- Vert pour entités factices
                Color(0, 0, 255, 100),   -- Bleu pour audio
                Color(255, 255, 0, 100), -- Jaune pour effets d'écran
                Color(255, 0, 255, 100), -- Magenta pour faux joueurs
                Color(0, 255, 255, 100)  -- Cyan pour paranoïa
            }
            trigger:SetColor(colors[i])
        end
    end
    
    ply:PrintMessage(HUD_PRINTCONSOLE, "6 triggers d'hallucination spawnés autour de vous.")
end)

-- Commande pour nettoyer tous les triggers
concommand.Add("hallucination_cleanup", function(ply, cmd, args)
    if not ply:IsAdmin() then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Vous devez être administrateur pour utiliser cette commande.")
        return
    end
    
    local count = 0
    for _, ent in pairs(ents.FindByClass("hallucination_trigger")) do
        if IsValid(ent) then
            ent:Remove()
            count = count + 1
        end
    end
    
    ply:PrintMessage(HUD_PRINTCONSOLE, count .. " triggers d'hallucination supprimés.")
end)

-- Commande pour arrêter toutes les hallucinations
concommand.Add("hallucination_stop_all", function(ply, cmd, args)
    if not ply:IsAdmin() then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Vous devez être administrateur pour utiliser cette commande.")
        return
    end
    
    local count = 0
    for steamID, data in pairs(HallucinatingPlayers) do
        if IsValid(data.player) then
            net.Start("StopHallucination")
            net.Send(data.player)
            count = count + 1
        end
    end
    
    HallucinatingPlayers = {}
    
    ply:PrintMessage(HUD_PRINTCONSOLE, "Toutes les hallucinations arrêtées (" .. count .. " joueurs).")
end)

-- Commande pour obtenir des informations sur l'addon
concommand.Add("hallucination_info", function(ply, cmd, args)
    ply:PrintMessage(HUD_PRINTCONSOLE, "=== Hallucination Addon Info ===")
    ply:PrintMessage(HUD_PRINTCONSOLE, "Version: 1.0")
    ply:PrintMessage(HUD_PRINTCONSOLE, "Types d'hallucinations: 6")
    ply:PrintMessage(HUD_PRINTCONSOLE, "Joueurs actuellement en hallucination: " .. table.Count(HallucinatingPlayers))
    ply:PrintMessage(HUD_PRINTCONSOLE, "")
    ply:PrintMessage(HUD_PRINTCONSOLE, "Commandes disponibles:")
    ply:PrintMessage(HUD_PRINTCONSOLE, "- hallucination_start <joueur> [type] [durée] [intensité]")
    ply:PrintMessage(HUD_PRINTCONSOLE, "- hallucination_stop <joueur>")
    ply:PrintMessage(HUD_PRINTCONSOLE, "- hallucination_list")
    ply:PrintMessage(HUD_PRINTCONSOLE, "- hallucination_test_all")
    ply:PrintMessage(HUD_PRINTCONSOLE, "- hallucination_spawn_triggers")
    ply:PrintMessage(HUD_PRINTCONSOLE, "- hallucination_cleanup")
    ply:PrintMessage(HUD_PRINTCONSOLE, "- hallucination_stop_all")
end)
