-- Heavy Armor Spawner Entity - Server Side

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    self:SetModel("models/props_lab/huladoll.mdl")
    self:SetModelScale(1.5, 0) -- Plus grand pour l'armure lourde
    
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetUseType(SIMPLE_USE)
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:EnableMotion(false)
    end
    
    -- Couleur orange pour l'Armure Lourde
    self:SetColor(Color(255, 150, 0, 255))
    
    self.LastUseTime = 0
    self.UseDelay = 1
end

function ENT:Use(activator, caller)
    if not <PERSON><PERSON>alid(activator) or not activator:IsPlayer() then return end
    
    if CurTime() - self.LastUseTime < self.UseDelay then return end
    self.LastUseTime = CurTime()
    
    if not HEAVY_ARMOR then
        activator:ChatPrint("Erreur: Système Armure Lourde non initialisé!")
        return
    end
    
    -- Gestion de l'échange
    if activator.heavy_exchange_ready then
        activator.heavy_exchange_ready = nil
        HEAVY_ARMOR:EquipPlayer(activator)
        return
    end
    
    -- Vérifier les autres combinaisons
    if UTS_SUIT and UTS_SUIT.EquippedPlayers[activator:SteamID()] then
        activator:ChatPrint("Échanger l'UTS MkVII contre l'Armure Lourde Mk III ? Utilisez à nouveau pour confirmer.")
        activator.heavy_exchange_ready = true
        timer.Simple(5, function() if IsValid(activator) then activator.heavy_exchange_ready = nil end end)
        return
    elseif INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[activator:SteamID()] then
        activator:ChatPrint("Échanger l'Infiltration Mk IV contre l'Armure Lourde Mk III ? Utilisez à nouveau pour confirmer.")
        activator.heavy_exchange_ready = true
        timer.Simple(5, function() if IsValid(activator) then activator.heavy_exchange_ready = nil end end)
        return
    elseif COMBAT_SUIT and COMBAT_SUIT.EquippedPlayers[activator:SteamID()] then
        activator:ChatPrint("Échanger le Combat Mk II contre l'Armure Lourde Mk III ? Utilisez à nouveau pour confirmer.")
        activator.heavy_exchange_ready = true
        timer.Simple(5, function() if IsValid(activator) then activator.heavy_exchange_ready = nil end end)
        return
    elseif HEAVY_ARMOR.EquippedPlayers[activator:SteamID()] then
        HEAVY_ARMOR:UnequipPlayer(activator)
        return
    end
    
    HEAVY_ARMOR:EquipPlayer(activator)
end

function ENT:Think()
    local ang = self:GetAngles()
    ang.y = ang.y + 0.3 -- Plus lent car plus lourd
    self:SetAngles(ang)
    
    self:NextThink(CurTime() + 0.1)
    return true
end
