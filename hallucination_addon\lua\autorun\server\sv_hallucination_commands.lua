-- Hallucination Admin Commands
-- Commandes pour les administrateurs pour contrôler les hallucinations

-- Fonction pour démarrer une hallucination sur un joueur
local function StartPlayerHallucination(target, halluType, duration, intensity)
    if not IsValid(target) or not target:IsPlayer() then return false end
    
    local steamID = target:SteamID()
    
    -- Vérifier si le joueur n'est pas déjà en hallucination
    if HallucinatingPlayers[steamID] then
        return false, "Le joueur est déjà en hallucination"
    end
    
    -- Valeurs par défaut
    halluType = halluType or HALLUCINATION_TYPES.VISUAL_DISTORTION
    duration = duration or HALLUCINATION_DURATIONS.MEDIUM
    intensity = intensity or 1.0
    
    -- Mar<PERSON> le joueur comme hallucinant
    HallucinatingPlayers[steamID] = {
        player = target,
        startTime = CurTime(),
        duration = duration,
        type = halluType,
        intensity = intensity
    }
    
    -- Envoyer l'hallucination au client
    net.Start("StartHallucination")
    net.WriteInt(halluType, 8)
    net.WriteFloat(duration)
    net.WriteFloat(intensity)
    net.Send(target)
    
    -- Message d'avertissement
    local startMessage = GetRandomHallucinationMessage("START")
    target:PrintMessage(HUD_PRINTCENTER, startMessage)

    -- Programmer l'arrêt de l'hallucination
    timer.Simple(duration, function()
        if IsValid(target) and HallucinatingPlayers[steamID] then
            HallucinatingPlayers[steamID] = nil
            net.Start("StopHallucination")
            net.Send(target)
            local endMessage = GetRandomHallucinationMessage("END")
            target:PrintMessage(HUD_PRINTCENTER, endMessage)
        end
    end)
    
    return true
end

-- Fonction pour arrêter une hallucination
local function StopPlayerHallucination(target)
    if not IsValid(target) or not target:IsPlayer() then return false end
    
    local steamID = target:SteamID()
    
    if not HallucinatingPlayers[steamID] then
        return false, "Le joueur n'est pas en hallucination"
    end
    
    HallucinatingPlayers[steamID] = nil
    net.Start("StopHallucination")
    net.Send(target)
    target:PrintMessage(HUD_PRINTCENTER, "L'hallucination s'arrête brutalement...")
    
    return true
end

-- Commande console pour démarrer une hallucination
concommand.Add("hallucination_start", function(ply, cmd, args)
    if not ply:IsAdmin() then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Vous devez être administrateur pour utiliser cette commande.")
        return
    end
    
    if #args < 1 then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Usage: hallucination_start <joueur> [type] [durée] [intensité]")
        ply:PrintMessage(HUD_PRINTCONSOLE, "Types: 1=Distorsion, 2=Entités factices, 3=Audio, 4=Écran, 5=Faux joueurs, 6=Paranoïa")
        return
    end
    
    local targetName = args[1]
    local target = nil
    
    -- Trouver le joueur cible
    for _, p in pairs(player.GetAll()) do
        if string.find(string.lower(p:Name()), string.lower(targetName)) then
            target = p
            break
        end
    end
    
    if not IsValid(target) then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Joueur introuvable: " .. targetName)
        return
    end
    
    local halluType = tonumber(args[2]) or HALLUCINATION_TYPES.VISUAL_DISTORTION
    local duration = tonumber(args[3]) or HALLUCINATION_DURATIONS.MEDIUM
    local intensity = tonumber(args[4]) or 1.0
    
    local success, error = StartPlayerHallucination(target, halluType, duration, intensity)
    
    if success then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Hallucination démarrée sur " .. target:Name())
        PrintMessage(HUD_PRINTCONSOLE, ply:Name() .. " a déclenché une hallucination sur " .. target:Name())
    else
        ply:PrintMessage(HUD_PRINTCONSOLE, "Erreur: " .. (error or "Inconnue"))
    end
end)

-- Commande console pour arrêter une hallucination
concommand.Add("hallucination_stop", function(ply, cmd, args)
    if not ply:IsAdmin() then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Vous devez être administrateur pour utiliser cette commande.")
        return
    end
    
    if #args < 1 then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Usage: hallucination_stop <joueur>")
        return
    end
    
    local targetName = args[1]
    local target = nil
    
    -- Trouver le joueur cible
    for _, p in pairs(player.GetAll()) do
        if string.find(string.lower(p:Name()), string.lower(targetName)) then
            target = p
            break
        end
    end
    
    if not IsValid(target) then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Joueur introuvable: " .. targetName)
        return
    end
    
    local success, error = StopPlayerHallucination(target)
    
    if success then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Hallucination arrêtée pour " .. target:Name())
    else
        ply:PrintMessage(HUD_PRINTCONSOLE, "Erreur: " .. (error or "Inconnue"))
    end
end)

-- Commande pour lister les joueurs en hallucination
concommand.Add("hallucination_list", function(ply, cmd, args)
    if not ply:IsAdmin() then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Vous devez être administrateur pour utiliser cette commande.")
        return
    end
    
    ply:PrintMessage(HUD_PRINTCONSOLE, "=== Joueurs en hallucination ===")
    
    local count = 0
    for steamID, data in pairs(HallucinatingPlayers) do
        if IsValid(data.player) then
            local timeLeft = data.duration - (CurTime() - data.startTime)
            ply:PrintMessage(HUD_PRINTCONSOLE, string.format("%s - Type: %d, Temps restant: %.1fs", 
                data.player:Name(), data.type, timeLeft))
            count = count + 1
        end
    end
    
    if count == 0 then
        ply:PrintMessage(HUD_PRINTCONSOLE, "Aucun joueur en hallucination.")
    end
end)
