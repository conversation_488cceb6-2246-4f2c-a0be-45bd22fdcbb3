if SERVER then
    util.AddNetworkString("RequestSpecialization")
    util.AddNetworkString("SendSpecializationChoice")

    local jobSpecializations = {}

    -- Populate specializations after jobs are loaded
    timer.Simple(5, function()
        if TEAM_AIT then
            jobSpecializations[TEAM_AIT] = {"UIT Lourd", "Technicien", "Analyste"}
        end
        if TEAM_RAIT then
            jobSpecializations[TEAM_RAIT] = {"Recrue Basique", "Recrue Avancée"}
        end
        -- Add more jobs and their specializations here
        print("Job specializations loaded:", table.Count(jobSpecializations))
    end)

    hook.Add("PlayerChangedTeam", "AskForSpecialization", function(ply, oldTeam, newTeam)
        print("Player changed team to:", newTeam)
        local specs = jobSpecializations[newTeam]
        if specs then
            print("Sending specialization request for team:", newTeam)
            net.Start("RequestSpecialization")
            net.WriteTable(specs)
            net.Send(ply)
        else
            print("No specializations for team:", newTeam)
        end
    end)

    net.Receive("SendSpecializationChoice", function(len, ply)
        local choice = net.ReadString()
        ply:SetPData("specialization", choice)
        ply:ChatPrint("Vous avez choisi la spécialisation: " .. choice)
        print("Player chose specialization:", choice)
    end)

    -- Function to get player's specialization
    function ply:GetSpecialization()
        return self:GetPData("specialization") or "Aucune"
    end
else
    net.Receive("RequestSpecialization", function()
        local specs = net.ReadTable()

        local frame = vgui.Create("DFrame")
        frame:SetTitle("Choisissez votre spécialisation")
        frame:SetSize(400, 300)
        frame:Center()
        frame:MakePopup()

        local label = vgui.Create("DLabel", frame)
        label:SetText("Veuillez choisir une spécialisation pour votre job:")
        label:Dock(TOP)
        label:SetContentAlignment(5)
        label:SetFont("DermaLarge")
        label:DockMargin(0, 10, 0, 10)

        local list = vgui.Create("DListView", frame)
        list:Dock(FILL)
        list:AddColumn("Spécialisations")

        for _, spec in ipairs(specs) do
            list:AddLine(spec)
        end

        local button = vgui.Create("DButton", frame)
        button:SetText("Confirmer")
        button:Dock(BOTTOM)
        button:SetDisabled(true)

        local selectedSpec = nil

        list.OnRowSelected = function(lst, index, pnl)
            selectedSpec = pnl:GetColumnText(1)
            button:SetDisabled(false)
        end

        button.DoClick = function()
            if selectedSpec then
                net.Start("SendSpecializationChoice")
                net.WriteString(selectedSpec)
                net.SendToServer()
                frame:Close()
            end
        end
    end)
end
