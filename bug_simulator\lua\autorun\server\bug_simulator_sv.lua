-- Bug Simulator - Serveur
-- Simule des bugs aléatoires quand on spawn des props

util.AddNetworkString("BugEffect")
util.AddNetworkString("PropCorruption")
util.AddNetworkString("StaffBugMenu")
util.AddNetworkString("StaffBugEffect")
util.AddNetworkString("RequestPlayerList")

-- Configuration
local BUG_CHANCE = 0.15 -- 15% de chance de bug
local DESPAWN_CHANCE = 0.08 -- 8% de chance de despawn
local CORRUPTION_CHANCE = 0.12 -- 12% de chance d'effet de corruption

-- Table pour tracker les props "buggés"
local buggedProps = {}

-- Sons de bug disponibles
local bugSounds = {
    "ambient/machines/machine1_hit1.wav",
    "ambient/machines/machine1_hit2.wav", 
    "ambient/energy/electric_loop.wav",
    "ambient/energy/zap1.wav",
    "ambient/energy/zap2.wav",
    "ambient/energy/zap3.wav",
    "physics/metal/metal_computer_impact_bullet1.wav",
    "physics/metal/metal_computer_impact_bullet2.wav",
    "physics/metal/metal_computer_impact_bullet3.wav"
}

-- Fonction pour créer un effet de bug sur un prop
local function CreateBugEffect(prop, player)
    if not IsValid(prop) or not IsValid(player) then return end
    
    -- Marquer le prop comme buggé
    buggedProps[prop:EntIndex()] = {
        prop = prop,
        player = player,
        startTime = CurTime(),
        effectType = math.random(1, 4)
    }
    
    -- Envoyer l'effet visuel aux clients
    net.Start("BugEffect")
    net.WriteEntity(prop)
    net.WriteInt(math.random(1, 4), 4) -- Type d'effet
    net.Broadcast()
    
    -- Jouer un son de bug
    local sound = bugSounds[math.random(#bugSounds)]
    prop:EmitSound(sound, 75, math.random(80, 120))
    
    -- Effet physique aléatoire
    local effectType = math.random(1, 5)
    
    if effectType == 1 then
        -- Faire trembler le prop
        timer.Create("BugShake_" .. prop:EntIndex(), 0.1, 30, function()
            if IsValid(prop) then
                local pos = prop:GetPos()
                local shake = Vector(
                    math.random(-5, 5),
                    math.random(-5, 5), 
                    math.random(-2, 2)
                )
                prop:SetPos(pos + shake)
            end
        end)
        
    elseif effectType == 2 then
        -- Faire clignoter le prop (invisible/visible)
        timer.Create("BugFlicker_" .. prop:EntIndex(), 0.2, 25, function()
            if IsValid(prop) then
                local alpha = prop:GetColor().a == 255 and 0 or 255
                prop:SetColor(Color(255, 255, 255, alpha))
            end
        end)
        
    elseif effectType == 3 then
        -- Changer la taille aléatoirement
        local originalScale = prop:GetModelScale()
        timer.Create("BugScale_" .. prop:EntIndex(), 0.3, 20, function()
            if IsValid(prop) then
                local scale = originalScale * math.random(50, 150) / 100
                prop:SetModelScale(scale)
            end
        end)
        
        -- Restaurer la taille originale après
        timer.Simple(6, function()
            if IsValid(prop) then
                prop:SetModelScale(originalScale)
            end
        end)
        
    elseif effectType == 4 then
        -- Rotation folle
        timer.Create("BugRotate_" .. prop:EntIndex(), 0.05, 100, function()
            if IsValid(prop) then
                local ang = prop:GetAngles()
                ang:RotateAroundAxis(Vector(1, 0, 0), math.random(-10, 10))
                ang:RotateAroundAxis(Vector(0, 1, 0), math.random(-10, 10))
                ang:RotateAroundAxis(Vector(0, 0, 1), math.random(-10, 10))
                prop:SetAngles(ang)
            end
        end)
        
    else
        -- Téléportation aléatoire
        timer.Create("BugTeleport_" .. prop:EntIndex(), 1, 5, function()
            if IsValid(prop) then
                local pos = prop:GetPos()
                local newPos = pos + Vector(
                    math.random(-100, 100),
                    math.random(-100, 100),
                    math.random(-50, 50)
                )
                prop:SetPos(newPos)
                
                -- Effet sonore de téléportation
                prop:EmitSound("ambient/energy/zap" .. math.random(1, 3) .. ".wav", 60)
            end
        end)
    end
    
    -- Chance de despawn après quelques secondes
    timer.Simple(math.random(3, 8), function()
        if IsValid(prop) and math.random() < DESPAWN_CHANCE then
            -- Effet de despawn
            net.Start("PropCorruption")
            net.WriteEntity(prop)
            net.WriteVector(prop:GetPos())
            net.Broadcast()
            
            -- Son de despawn
            prop:EmitSound("ambient/energy/electric_loop.wav", 100, 50)
            
            -- Message au joueur
            if IsValid(player) then
                player:ChatPrint("[BUG] Votre prop a été corrompu et a disparu!")
            end
            
            -- Supprimer le prop après un délai
            timer.Simple(1, function()
                if IsValid(prop) then
                    prop:Remove()
                end
            end)
            
            -- Nettoyer la table
            buggedProps[prop:EntIndex()] = nil
        end
    end)
end

-- Hook quand un joueur spawn un prop
hook.Add("PlayerSpawnedProp", "BugSimulator_PropSpawn", function(player, model, entity)
    -- Vérifier si on doit créer un bug
    if math.random() < BUG_CHANCE then
        -- Délai aléatoire avant le bug (0.5 à 3 secondes)
        timer.Simple(math.random(0.5, 3), function()
            CreateBugEffect(entity, player)
        end)
    end
end)

-- Hook quand un joueur spawn un ragdoll
hook.Add("PlayerSpawnedRagdoll", "BugSimulator_RagdollSpawn", function(player, model, entity)
    if math.random() < BUG_CHANCE * 0.7 then -- Moins de chance pour les ragdolls
        timer.Simple(math.random(1, 4), function()
            CreateBugEffect(entity, player)
        end)
    end
end)

-- Hook quand un joueur spawn un véhicule
hook.Add("PlayerSpawnedVehicle", "BugSimulator_VehicleSpawn", function(player, entity)
    if math.random() < BUG_CHANCE * 0.5 then -- Encore moins pour les véhicules
        timer.Simple(math.random(2, 5), function()
            CreateBugEffect(entity, player)
        end)
    end
end)

-- Nettoyer les props supprimés de notre table
hook.Add("EntityRemoved", "BugSimulator_Cleanup", function(entity)
    if buggedProps[entity:EntIndex()] then
        buggedProps[entity:EntIndex()] = nil
        
        -- Nettoyer les timers associés
        timer.Remove("BugShake_" .. entity:EntIndex())
        timer.Remove("BugFlicker_" .. entity:EntIndex())
        timer.Remove("BugScale_" .. entity:EntIndex())
        timer.Remove("BugRotate_" .. entity:EntIndex())
        timer.Remove("BugTeleport_" .. entity:EntIndex())
    end
end)

-- Commande admin pour forcer un bug sur un prop
concommand.Add("bug_force", function(player, cmd, args)
    if not player:IsAdmin() then return end
    
    local trace = player:GetEyeTrace()
    if IsValid(trace.Entity) then
        CreateBugEffect(trace.Entity, player)
        player:ChatPrint("Bug forcé sur l'entité!")
    else
        player:ChatPrint("Aucune entité trouvée!")
    end
end)

-- Commande pour ajuster les chances de bug
concommand.Add("bug_chance", function(player, cmd, args)
    if not player:IsAdmin() then return end
    
    if args[1] then
        local newChance = tonumber(args[1])
        if newChance and newChance >= 0 and newChance <= 1 then
            BUG_CHANCE = newChance
            player:ChatPrint("Chance de bug définie à " .. (newChance * 100) .. "%")
        else
            player:ChatPrint("Valeur invalide! Utilisez un nombre entre 0 et 1")
        end
    else
        player:ChatPrint("Chance actuelle de bug: " .. (BUG_CHANCE * 100) .. "%")
    end
end)

-- Système de menu staff pour effets de bug (simplifié)
net.Receive("RequestPlayerList", function(len, player)
    if not player:IsAdmin() and not player:IsSuperAdmin() then return end

    local players = {}
    for _, ply in ipairs(player.GetAll()) do
        table.insert(players, {
            name = ply:Name(),
            steamid = ply:SteamID(),
            userid = ply:UserID()
        })
    end

    net.Start("StaffBugMenu")
    net.WriteTable(players)
    net.Send(player)
end)

-- Recevoir les effets de bug du staff
net.Receive("StaffBugEffect", function(len, player)
    if not player:IsAdmin() and not player:IsSuperAdmin() then return end

    local targetUserID = net.ReadInt(32)
    local effectType = net.ReadString()
    local duration = net.ReadInt(32)
    local intensity = net.ReadFloat()

    local targetPlayer = nil
    for _, ply in ipairs(player.GetAll()) do
        if ply:UserID() == targetUserID then
            targetPlayer = ply
            break
        end
    end

    if not IsValid(targetPlayer) then
        player:ChatPrint("[Bug Simulator] Joueur introuvable!")
        return
    end

    -- Envoyer l'effet au client cible
    net.Start("StaffBugEffect")
    net.WriteString(effectType)
    net.WriteInt(duration, 32)
    net.WriteFloat(intensity)
    net.Send(targetPlayer)

    -- Message de confirmation
    player:ChatPrint("[Bug Simulator] Effet '" .. effectType .. "' envoyé à " .. targetPlayer:Name())

    -- Log pour les admins
    print("[Bug Simulator] " .. player:Name() .. " a envoyé l'effet '" .. effectType .. "' à " .. targetPlayer:Name())
end)

-- Commande pour ouvrir le menu staff (simplifiée - le menu s'ouvre côté client)
concommand.Add("bug_menu_server", function(player, cmd, args)
    if not player:IsAdmin() and not player:IsSuperAdmin() then
        player:ChatPrint("[Bug Simulator] Vous devez être staff pour utiliser cette commande!")
        return
    end

    player:ChatPrint("[Bug Simulator] Utilisez 'bug_menu' côté client pour ouvrir le menu!")
end)

print("[Bug Simulator] Addon chargé - Chance de bug: " .. (BUG_CHANCE * 100) .. "%")
print("[Bug Simulator] Commande staff: bug_menu")
