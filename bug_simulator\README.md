# Bug Simulator - <PERSON><PERSON>'s Mod

## Description
Cet addon simule des "bugs" aléatoires quand les joueurs spawnent des props, ragdolls ou véhicules. Il crée des effets visuels de corruption et peut faire despawn les objets de manière dramatique.

## Fonctionnalités

### Effets de Bug Aléatoires
- **15% de chance** qu'un prop spawné soit affecté par un bug
- **8% de chance** que le prop disparaisse complètement (despawn)
- **12% de chance** d'effets de corruption visuelle

### Types d'Effets Physiques
1. **Tremblement** - Le prop tremble de manière incontrôlable
2. **Clignotement** - Le prop devient invisible/visible rapidement
3. **Changement de taille** - Le prop change de taille aléatoirement
4. **Rotation folle** - Le prop tourne dans tous les sens
5. **Téléportation** - Le prop se téléporte à des positions aléatoires

### Effets Visuels
- Particules de corruption rouges
- Lignes de glitch à l'écran
- Messages d'erreur type "ERROR 404", "MEMORY LEAK", etc.
- Corruption temporaire de l'écran
- Effets de post-processing (couleurs altérées, motion blur)
- Overlay de corruption avec lignes horizontales et blocs aléatoires

### Sons
- Sons électriques et de machines pour les bugs
- Sons de téléportation
- Sons de corruption lors du despawn

## Commandes Admin

### `bug_force`
Force un bug sur l'entité que vous regardez.
```
bug_force
```

### `bug_chance <valeur>`
Modifie la chance de bug (valeur entre 0 et 1).
```
bug_chance 0.25  // 25% de chance
bug_chance 0.1   // 10% de chance
```

### `bug_menu`
**NOUVELLE FONCTIONNALITÉ** - Ouvre le menu staff pour appliquer des effets de bug/glitch sur l'écran des joueurs.
```
bug_menu
```

## Menu Staff - Effets d'Écran

Le menu staff permet d'appliquer des effets visuels de bug/glitch directement sur l'écran des joueurs. Accessible uniquement aux admins et super-admins.

### Effets Disponibles

#### 🟢 **Matrix Rain**
- Pluie de code Matrix vert qui tombe sur l'écran
- Effet de post-processing vert/noir
- Intensité contrôle le nombre de colonnes

#### 🔴 **Screen Glitch**
- Glitches visuels intenses avec distorsion
- Lignes horizontales colorées
- Motion blur et corruption des couleurs
- Effet très perturbant

#### 📺 **Digital Noise**
- Bruit numérique aléatoire (pixels colorés)
- Couleurs qui changent constamment
- Post-processing aléatoire
- Simule les interférences TV

#### 💻 **Code Corruption**
- Affiche du code qui se corrompt progressivement
- Messages d'erreur qui tremblent
- Caractères qui changent aléatoirement
- Couleur rouge d'erreur

#### 📟 **Scan Lines**
- Simule un vieux moniteur CRT
- Lignes de scan horizontales
- Effet de courbure d'écran
- Post-processing vert/vintage

#### 🌈 **Pixel Storm**
- Tempête de pixels colorés qui bougent
- Particules qui rebondissent sur les bords
- Effet très dynamique et coloré

#### ⚠️ **System Error**
- Messages d'erreur système partout
- "FATAL ERROR", "CRITICAL", etc.
- Texte rouge qui tremble
- Simule un crash système

#### 💙 **Blue Screen**
- Écran bleu de la mort Windows
- Texte blanc sur fond bleu
- Message d'erreur complet
- Post-processing bleu intense

### Configuration des Effets

Pour chaque effet, vous pouvez configurer :
- **Durée** : 1 à 60 secondes
- **Intensité** : 0.1 à 3.0 (affecte la force de l'effet)
- **Cible** : N'importe quel joueur connecté

### Utilisation du Menu

1. Tapez `bug_menu` dans la console
2. Sélectionnez un joueur dans la liste
3. Choisissez un effet de bug/glitch
4. Configurez la durée et l'intensité
5. Cliquez sur "Appliquer l'effet"

### Permissions

- Seuls les **admins** et **super-admins** peuvent utiliser le menu
- Les effets sont loggés dans la console serveur
- Messages de confirmation dans le chat

## Configuration
Vous pouvez modifier les variables dans le fichier serveur :
- `BUG_CHANCE` - Chance qu'un bug se produise (défaut: 0.15)
- `DESPAWN_CHANCE` - Chance qu'un prop disparaisse (défaut: 0.08)
- `CORRUPTION_CHANCE` - Chance d'effet de corruption (défaut: 0.12)

## Installation
1. Placez le dossier `bug_simulator` dans votre dossier `addons`
2. Redémarrez votre serveur ou changez de map
3. L'addon se charge automatiquement

## Compatibilité
- Compatible avec tous les modes de jeu
- Fonctionne avec les props, ragdolls et véhicules
- Effets visuels optimisés pour les performances

## Notes
- Les effets de props sont temporaires (10 secondes maximum)
- Les effets staff peuvent durer jusqu'à 60 secondes
- Les timers sont automatiquement nettoyés
- L'addon n'affecte pas les performances du serveur
- Les effets visuels sont uniquement côté client
- Le menu staff utilise une interface graphique intuitive
- Tous les effets staff sont réversibles et temporaires

## Exemples d'Utilisation Staff

```lua
-- Ouvrir le menu
bug_menu

-- Appliquer un effet Matrix de 30 secondes à intensité 2.0 sur un joueur
-- (via l'interface graphique)

-- Forcer un bug sur un prop
bug_force

-- Changer les chances de bug automatique
bug_chance 0.3  // 30% de chance
```

## Sécurité

- Aucun effet permanent
- Pas d'impact sur les données de jeu
- Effets purement cosmétiques
- Système de permissions robuste
- Logging des actions staff

Amusez-vous bien avec les bugs ! 🐛✨
