# Bug Simulator - <PERSON><PERSON>'s Mod

## Description
Cet addon simule des "bugs" aléatoires quand les joueurs spawnent des props, ragdolls ou véhicules. Il crée des effets visuels de corruption et peut faire despawn les objets de manière dramatique.

## Fonctionnalités

### Effets de Bug Aléatoires
- **15% de chance** qu'un prop spawné soit affecté par un bug
- **8% de chance** que le prop disparaisse complètement (despawn)
- **12% de chance** d'effets de corruption visuelle

### Types d'Effets Physiques
1. **Tremblement** - Le prop tremble de manière incontrôlable
2. **Clignotement** - Le prop devient invisible/visible rapidement
3. **Changement de taille** - Le prop change de taille aléatoirement
4. **Rotation folle** - Le prop tourne dans tous les sens
5. **Téléportation** - Le prop se téléporte à des positions aléatoires

### Effets Visuels
- Particules de corruption rouges
- Lignes de glitch à l'écran
- Messages d'erreur type "ERROR 404", "MEMORY LEAK", etc.
- Corruption temporaire de l'écran
- Effets de post-processing (couleurs altérées, motion blur)
- Overlay de corruption avec lignes horizontales et blocs aléatoires

### Sons
- Sons électriques et de machines pour les bugs
- Sons de téléportation
- Sons de corruption lors du despawn

## Commandes Admin

### `bug_force`
Force un bug sur l'entité que vous regardez.
```
bug_force
```

### `bug_chance <valeur>`
Modifie la chance de bug (valeur entre 0 et 1).
```
bug_chance 0.25  // 25% de chance
bug_chance 0.1   // 10% de chance
```

## Configuration
Vous pouvez modifier les variables dans le fichier serveur :
- `BUG_CHANCE` - Chance qu'un bug se produise (défaut: 0.15)
- `DESPAWN_CHANCE` - Chance qu'un prop disparaisse (défaut: 0.08)
- `CORRUPTION_CHANCE` - Chance d'effet de corruption (défaut: 0.12)

## Installation
1. Placez le dossier `bug_simulator` dans votre dossier `addons`
2. Redémarrez votre serveur ou changez de map
3. L'addon se charge automatiquement

## Compatibilité
- Compatible avec tous les modes de jeu
- Fonctionne avec les props, ragdolls et véhicules
- Effets visuels optimisés pour les performances

## Notes
- Les effets sont temporaires (10 secondes maximum)
- Les timers sont automatiquement nettoyés
- L'addon n'affecte pas les performances du serveur
- Les effets visuels sont uniquement côté client

Amusez-vous bien avec les bugs ! 🐛
