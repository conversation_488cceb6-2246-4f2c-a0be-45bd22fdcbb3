-- Combat Mk II Suit - Server Side
-- Combinaison hybride avec protection et camouflage

-- Configuration
COMBAT_SUIT = COMBAT_SUIT or {}
COMBAT_SUIT.Config = {
    ArmorAmount = 100, -- Points d'armure
    InvisibilityDelay = 3, -- <PERSON><PERSON><PERSON> avant invisibilité complète (secondes)
    MovementThreshold = 50, -- Seuil de mouvement pour perdre l'invisibilité
    PartialAlpha = 80, -- Transparence partielle en mouvement (moins que l'Infiltration)
}

-- Table des joueurs équipés
COMBAT_SUIT.EquippedPlayers = COMBAT_SUIT.EquippedPlayers or {}

-- Fonction pour équiper la combinaison
function COMBAT_SUIT:EquipPlayer(ply)
    if not IsValid(ply) then return false end
    
    -- Vérifier si le joueur porte déjà une autre combinaison
    if UTS_SUIT and UTS_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange de combinaison : UTS MkVII → Combat Mk II")
        UTS_SUIT:UnequipPlayer(ply)
    elseif INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange de combinaison : Infiltration Mk IV → Combat Mk II")
        INFILTRATION_SUIT:UnequipPlayer(ply)
    elseif HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Échange d'équipement : Armure Lourde Mk III → Combat Mk II")
        HEAVY_ARMOR:UnequipPlayer(ply)
    end
    
    -- Vérifier si le joueur porte déjà cette combinaison
    if self.EquippedPlayers[ply:SteamID()] then
        ply:ChatPrint("Vous portez déjà le Combat Mk II!")
        return false
    end
    
    -- Équiper la combinaison
    self.EquippedPlayers[ply:SteamID()] = {
        lastMovement = CurTime(),
        isInvisible = false,
        lastPosition = ply:GetPos(),
        originalArmor = ply:Armor()
    }
    
    ply:SetArmor(self.Config.ArmorAmount)
    
    -- Envoyer au client
    net.Start("COMBAT_Equip")
    net.Send(ply)
    
    ply:ChatPrint("Combat Mk II activé - Systèmes de protection et camouflage opérationnels")
    
    return true
end

-- Fonction pour déséquiper la combinaison
function COMBAT_SUIT:UnequipPlayer(ply)
    if not IsValid(ply) then return false end
    
    local suitData = self.EquippedPlayers[ply:SteamID()]
    if not suitData then
        ply:ChatPrint("Vous ne portez pas le Combat Mk II!")
        return false
    end
    
    -- Restaurer la visibilité et l'armure
    ply:SetColor(Color(255, 255, 255, 255))
    ply:SetRenderMode(RENDERMODE_NORMAL)
    ply:SetArmor(suitData.originalArmor)
    
    -- Retirer de la liste
    self.EquippedPlayers[ply:SteamID()] = nil
    
    -- Envoyer au client
    net.Start("COMBAT_Unequip")
    net.Send(ply)
    
    ply:ChatPrint("Combat Mk II désactivé")
    
    return true
end

-- Hook pour gérer le camouflage
hook.Add("Think", "COMBAT_CamoSystem", function()
    for steamID, suitData in pairs(COMBAT_SUIT.EquippedPlayers) do
        local ply = player.GetBySteamID(steamID)
        if IsValid(ply) then
            local currentPos = ply:GetPos()
            local movement = currentPos:Distance(suitData.lastPosition)
            
            -- Détecter le mouvement
            if movement > COMBAT_SUIT.Config.MovementThreshold then
                suitData.lastMovement = CurTime()
                suitData.lastPosition = currentPos
                
                -- Transparence partielle en mouvement (moins efficace que l'Infiltration)
                if suitData.isInvisible then
                    ply:SetColor(Color(255, 255, 255, COMBAT_SUIT.Config.PartialAlpha))
                    ply:SetRenderMode(RENDERMODE_TRANSALPHA)
                    suitData.isInvisible = false
                    
                    -- Envoyer au client
                    net.Start("COMBAT_CamoUpdate")
                    net.WriteString("partial")
                    net.Send(ply)
                end
            else
                -- Immobile - devenir invisible après délai
                if not suitData.isInvisible and CurTime() - suitData.lastMovement > COMBAT_SUIT.Config.InvisibilityDelay then
                    ply:SetColor(Color(255, 255, 255, 0))
                    ply:SetRenderMode(RENDERMODE_TRANSALPHA)
                    suitData.isInvisible = true
                    
                    -- Envoyer au client
                    net.Start("COMBAT_CamoUpdate")
                    net.WriteString("invisible")
                    net.Send(ply)
                end
            end
        end
    end
end)

-- Nettoyage à la déconnexion
hook.Add("PlayerDisconnected", "COMBAT_Cleanup", function(ply)
    if COMBAT_SUIT.EquippedPlayers[ply:SteamID()] then
        COMBAT_SUIT.EquippedPlayers[ply:SteamID()] = nil
    end
end)

-- Nettoyage à la mort
hook.Add("PlayerDeath", "COMBAT_Death", function(victim, inflictor, attacker)
    if COMBAT_SUIT.EquippedPlayers[victim:SteamID()] then
        COMBAT_SUIT:UnequipPlayer(victim)
    end
end)

-- Netmessages
util.AddNetworkString("COMBAT_Equip")
util.AddNetworkString("COMBAT_Unequip")
util.AddNetworkString("COMBAT_CamoUpdate")
util.AddNetworkString("COMBAT_RequestEquip")
util.AddNetworkString("COMBAT_RequestUnequip")

-- Recevoir les demandes d'équipement
net.Receive("COMBAT_RequestEquip", function(len, ply)
    if not IsValid(ply) then return end
    
    -- Vérifier si le joueur porte une autre combinaison
    if (UTS_SUIT and UTS_SUIT.EquippedPlayers[ply:SteamID()]) or
       (INFILTRATION_SUIT and INFILTRATION_SUIT.EquippedPlayers[ply:SteamID()]) or
       (HEAVY_ARMOR and HEAVY_ARMOR.EquippedPlayers[ply:SteamID()]) then
        ply:ChatPrint("Vous portez déjà un équipement. Utilisez !combat unequip d'abord ou utilisez un spawner pour échanger.")
        return
    end
    
    COMBAT_SUIT:EquipPlayer(ply)
end)

net.Receive("COMBAT_RequestUnequip", function(len, ply)
    COMBAT_SUIT:UnequipPlayer(ply)
end)

print("Combat Mk II Suit - Server loaded")
