-- Combat Mk II Suit - Client Side
-- Interface HUD pour la combinaison hybride

-- Configuration client
COMBAT_SUIT_CLIENT = COMBAT_SUIT_CLIENT or {}
COMBAT_SUIT_CLIENT.Active = false
COMBAT_SUIT_CLIENT.CamoStatus = "visible"

-- Couleurs de l'interface (thème blanc/rouge)
local COLOR_BG = Color(30, 30, 30, 200)
local COLOR_BORDER = Color(255, 255, 255, 255)
local COLOR_TEXT = Color(255, 255, 255, 255)
local COLOR_ARMOR = Color(255, 200, 100, 255)
local COLOR_CAMO = Color(200, 200, 255, 255)

-- Fonction pour dessiner l'HUD
function COMBAT_SUIT_CLIENT:DrawHUD()
    if not self.Active then return end
    
    local scrW, scrH = ScrW(), ScrH()
    
    -- Panneau principal (position centre-haut)
    local panelW, panelH = 280, 100
    local x, y = scrW/2 - panelW/2, 20
    
    draw.RoundedBox(8, x, y, panelW, panelH, COLOR_BG)
    draw.RoundedBox(8, x, y, panelW, 25, COLOR_BORDER)
    
    -- Titre
    draw.SimpleText("COMBAT MK II", "DermaDefault", x + panelW/2, y + 12, COLOR_TEXT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Ligne de séparation
    draw.RoundedBox(0, x + 10, y + 30, panelW - 20, 1, COLOR_BORDER)
    
    -- Statut de l'armure
    draw.SimpleText("ARMURE: +100", "DermaDefault", x + 10, y + 40, COLOR_ARMOR)
    
    -- Statut du camouflage
    local camoText = "CAMOUFLAGE: "
    local camoColor = COLOR_CAMO
    
    if self.CamoStatus == "invisible" then
        camoText = camoText .. "ACTIF"
        camoColor = Color(100, 255, 100)
    elseif self.CamoStatus == "partial" then
        camoText = camoText .. "PARTIEL"
        camoColor = Color(255, 200, 100)
    else
        camoText = camoText .. "INACTIF"
        camoColor = Color(255, 100, 100)
    end
    
    draw.SimpleText(camoText, "DermaDefault", x + 10, y + 55, camoColor)
    
    -- Mode hybride
    draw.SimpleText("MODE: COMBAT FURTIF", "DermaDefault", x + 10, y + 75, COLOR_TEXT)
end

-- Fonction pour activer la combinaison
function COMBAT_SUIT_CLIENT:Activate()
    self.Active = true
    self.CamoStatus = "visible"
    
    -- Désactiver les autres HUDs
    if UTS_SUIT_CLIENT then UTS_SUIT_CLIENT.Active = false end
    if INFILTRATION_SUIT_CLIENT then INFILTRATION_SUIT_CLIENT.Active = false end
    if HEAVY_ARMOR_CLIENT then HEAVY_ARMOR_CLIENT.Active = false end
    
    chat.AddText(Color(255, 255, 255), "[Combat Mk II] ", Color(255, 255, 255), "Systèmes hybrides activés")
end

-- Fonction pour désactiver la combinaison
function COMBAT_SUIT_CLIENT:Deactivate()
    self.Active = false
    self.CamoStatus = "visible"
    
    chat.AddText(Color(255, 100, 100), "[Combat Mk II] ", Color(255, 255, 255), "Systèmes hybrides désactivés")
end

-- Hook pour dessiner le HUD
hook.Add("HUDPaint", "COMBAT_DrawHUD", function()
    COMBAT_SUIT_CLIENT:DrawHUD()
end)

-- Désactiver automatiquement la combinaison quand le joueur meurt
hook.Add("PlayerDeath", "COMBAT_ClientPlayerDeath", function(victim, inflictor, attacker)
    if victim == LocalPlayer() and COMBAT_SUIT_CLIENT.Active then
        COMBAT_SUIT_CLIENT:Deactivate()
    end
end)

-- Netmessages
net.Receive("COMBAT_Equip", function()
    COMBAT_SUIT_CLIENT:Activate()
end)

net.Receive("COMBAT_Unequip", function()
    COMBAT_SUIT_CLIENT:Deactivate()
end)

net.Receive("COMBAT_CamoUpdate", function()
    local status = net.ReadString()
    COMBAT_SUIT_CLIENT.CamoStatus = status
end)

print("Combat Mk II Suit - Client loaded")
