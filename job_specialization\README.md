# Job Specialization Addon for DarkRP

This addon adds a specialization choice when a player selects a job in DarkRP.

## Installation

1. Place the `job_specialization` folder in your `garrysmod/addons/` directory.
2. Ensure it loads after your jobs are defined (e.g., after darkrpmodification-master).

## Configuration

Edit the `jobSpecializations` table in `lua/autorun/job_specialization.lua` to add specializations for your jobs.

Example:
```lua
local jobSpecializations = {
    [TEAM_POLICE] = {"Patrouilleur", "Détective", "SWAT"},
    [TEAM_MEDIC] = {"Médecin Général", "Chirurgien", "Urgentiste"},
    -- Add more jobs
}
```

## Usage

When a player selects a job that has specializations defined, a menu will appear allowing them to choose their specialization.

The chosen specialization is stored in the player's PData and can be retrieved with `ply:GetSpecialization()`.

## Notes

- Make sure the TEAM_ constants are defined before this addon loads.
- If a job has no specializations defined, no menu will appear.
- The specialization is saved per player and persists across sessions.
