AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    self:SetModel("models/hunter/blocks/cube1x1x1.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetMaterial("models/debug/debugwhite")
    self:SetColor(Color(255, 0, 255, 100)) -- Magenta semi-transparent
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
    end
    
    -- Configuration par défaut
    self:SetHallucinationType(HALLUCINATION_TYPES.VISUAL_DISTORTION)
    self:SetHallucinationDuration(HALLUCINATION_DURATIONS.MEDIUM)
    self:SetHallucinationIntensity(1.0)
end

function ENT:SetHallucinationType(halluType)
    self:SetNWInt("HallucinationType", halluType)
end

function ENT:GetHallucinationType()
    return self:GetNWInt("HallucinationType", HALLUCINATION_TYPES.VISUAL_DISTORTION)
end

function ENT:SetHallucinationDuration(duration)
    self:SetNWFloat("HallucinationDuration", duration)
end

function ENT:GetHallucinationDuration()
    return self:GetNWFloat("HallucinationDuration", HALLUCINATION_DURATIONS.MEDIUM)
end

function ENT:SetHallucinationIntensity(intensity)
    self:SetNWFloat("HallucinationIntensity", intensity)
end

function ENT:GetHallucinationIntensity()
    return self:GetNWFloat("HallucinationIntensity", 1.0)
end

function ENT:TriggerHallucination(player)
    if not IsValid(player) or not player:IsPlayer() then return end
    
    local steamID = player:SteamID()
    
    -- Vérifier si le joueur n'est pas déjà en hallucination
    if HallucinatingPlayers[steamID] then
        player:PrintMessage(HUD_PRINTCENTER, "Vous êtes déjà en hallucination...")
        return
    end
    
    -- Marquer le joueur comme hallucinant
    HallucinatingPlayers[steamID] = {
        player = player,
        startTime = CurTime(),
        duration = self:GetHallucinationDuration(),
        type = self:GetHallucinationType(),
        intensity = self:GetHallucinationIntensity()
    }
    
    -- Envoyer l'hallucination au client
    net.Start("StartHallucination")
    net.WriteInt(self:GetHallucinationType(), 8)
    net.WriteFloat(self:GetHallucinationDuration())
    net.WriteFloat(self:GetHallucinationIntensity())
    net.Send(player)
    
    -- Message d'avertissement
    player:PrintMessage(HUD_PRINTCENTER, "La réalité commence à se distordre...")
    
    -- Programmer l'arrêt de l'hallucination
    timer.Simple(self:GetHallucinationDuration(), function()
        if IsValid(player) and HallucinatingPlayers[steamID] then
            HallucinatingPlayers[steamID] = nil
            net.Start("StopHallucination")
            net.Send(player)
            player:PrintMessage(HUD_PRINTCENTER, "La réalité revient lentement...")
        end
    end)
end

function ENT:Use(activator, caller)
    if activator:IsPlayer() then
        self:TriggerHallucination(activator)
    end
end

function ENT:Touch(ent)
    if ent:IsPlayer() then
        self:TriggerHallucination(ent)
    end
end
