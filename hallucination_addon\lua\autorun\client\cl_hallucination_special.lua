-- Hallucination Special Effects
-- Effets spéciaux avancés pour les hallucinations

local specialEffects = {}
local nextSpecialEffect = 0

-- Effet de vision tunnel
local function CreateTunnelVision()
    local effect = {
        type = "tunnel_vision",
        startTime = CurTime(),
        duration = math.random(3, 8),
        intensity = math.Rand(0.3, 0.8)
    }
    
    table.insert(specialEffects, effect)
end

-- Effet de vision floue
local function CreateBlurVision()
    local effect = {
        type = "blur_vision", 
        startTime = CurTime(),
        duration = math.random(2, 6),
        intensity = math.Rand(0.2, 0.6)
    }
    
    table.insert(specialEffects, effect)
end

-- Effet de couleurs inversées
local function CreateColorInversion()
    local effect = {
        type = "color_inversion",
        startTime = CurTime(), 
        duration = math.random(1, 4),
        intensity = math.Rand(0.5, 1.0)
    }
    
    table.insert(specialEffects, effect)
end

-- Effet de multiplicité (voir plusieurs copies de tout)
local function CreateMultiplicity()
    local effect = {
        type = "multiplicity",
        startTime = CurTime(),
        duration = math.random(2, 5),
        intensity = math.Rand(0.3, 0.7),
        offset = Vector(math.random(-50, 50), math.random(-50, 50), 0)
    }
    
    table.insert(specialEffects, effect)
end

-- Dessiner les effets spéciaux
local function DrawSpecialEffects()
    local scrW, scrH = ScrW(), ScrH()
    
    for i, effect in ipairs(specialEffects) do
        local elapsed = CurTime() - effect.startTime
        
        -- Supprimer les effets expirés
        if elapsed > effect.duration then
            table.remove(specialEffects, i)
            continue
        end
        
        -- Calculer l'alpha basé sur la durée
        local alpha = 1.0
        if elapsed < 0.5 then
            alpha = elapsed / 0.5 -- Fade in
        elseif elapsed > effect.duration - 0.5 then
            alpha = (effect.duration - elapsed) / 0.5 -- Fade out
        end
        
        alpha = alpha * effect.intensity
        
        if effect.type == "tunnel_vision" then
            -- Créer un effet de vision tunnel simplifié
            local centerX, centerY = scrW / 2, scrH / 2
            local radius = math.min(scrW, scrH) * (0.2 + 0.3 * (1 - alpha))

            -- Dessiner des rectangles noirs autour du centre pour simuler un tunnel
            surface.SetDrawColor(0, 0, 0, 200 * alpha)

            -- Haut
            surface.DrawRect(0, 0, scrW, centerY - radius)
            -- Bas
            surface.DrawRect(0, centerY + radius, scrW, scrH - (centerY + radius))
            -- Gauche
            surface.DrawRect(0, centerY - radius, centerX - radius, radius * 2)
            -- Droite
            surface.DrawRect(centerX + radius, centerY - radius, scrW - (centerX + radius), radius * 2)

            -- Ajouter un effet de vignette circulaire
            for i = 1, 20 do
                local ringRadius = radius + i * 10
                local ringAlpha = math.max(0, (20 - i) / 20) * alpha * 50

                surface.SetDrawColor(0, 0, 0, ringAlpha)

                -- Dessiner un anneau approximatif avec des rectangles
                local segments = 16
                for j = 0, segments - 1 do
                    local angle = (j / segments) * math.pi * 2
                    local x = centerX + math.cos(angle) * ringRadius
                    local y = centerY + math.sin(angle) * ringRadius
                    surface.DrawRect(x - 5, y - 5, 10, 10)
                end
            end
            
        elseif effect.type == "blur_vision" then
            -- Effet de vision floue (simulé avec des rectangles semi-transparents)
            local step = 40 -- Réduire le nombre de rectangles pour les performances
            for x = 0, scrW, step do
                for y = 0, scrH, step do
                    local offsetX = math.sin(CurTime() * 3 + x * 0.05) * 3 * alpha
                    local offsetY = math.cos(CurTime() * 3 + y * 0.05) * 3 * alpha
                    surface.SetDrawColor(255, 255, 255, 20 * alpha)
                    surface.DrawRect(x + offsetX, y + offsetY, step / 2, step / 2)
                end
            end
            
        elseif effect.type == "color_inversion" then
            -- Effet d'inversion des couleurs
            render.SetColorModulation(1 - alpha, 1 - alpha, 1 - alpha)
            
        elseif effect.type == "multiplicity" then
            -- Effet de multiplicité (dessiner des copies décalées)
            -- Cet effet est plus complexe et nécessiterait une approche différente
            -- Pour l'instant, on simule avec des lignes fantômes
            surface.SetDrawColor(255, 255, 255, 50 * alpha)
            for i = 1, 5 do
                local offsetX = effect.offset.x * i * 0.2
                local offsetY = effect.offset.y * i * 0.2
                surface.DrawLine(offsetX, 0, offsetX, scrH)
                surface.DrawLine(0, offsetY, scrW, offsetY)
            end
        end
    end
    
    -- Remettre la modulation de couleur normale
    render.SetColorModulation(1, 1, 1)
end

-- Fonction pour déclencher un effet spécial aléatoire
local function TriggerRandomSpecialEffect()
    if #specialEffects >= 3 then return end -- Limite le nombre d'effets simultanés
    
    local effectTypes = {
        CreateTunnelVision,
        CreateBlurVision, 
        CreateColorInversion,
        CreateMultiplicity
    }
    
    local randomEffect = effectTypes[math.random(1, #effectTypes)]
    randomEffect()
end

-- Hook pour dessiner les effets spéciaux
hook.Add("PostDrawHUD", "HallucinationSpecialEffects", function()
    if IsPlayerHallucinating() then
        DrawSpecialEffects()
        
        -- Déclencher occasionnellement des effets spéciaux
        if CurTime() > nextSpecialEffect and math.random() < 0.001 then
            TriggerRandomSpecialEffect()
            nextSpecialEffect = CurTime() + math.random(5, 15)
        end
    end
end)

-- Hook pour nettoyer les effets spéciaux
hook.Add("HallucinationStopped", "CleanupSpecialEffects", function()
    specialEffects = {}
    nextSpecialEffect = 0
    render.SetColorModulation(1, 1, 1)
end)

-- Hook pour les effets spéciaux déclenchés par le serveur
hook.Add("HallucinationSpecialEffect", "HandleSpecialEffect", function(effectType, data)
    if effectType == 1 then
        CreateTunnelVision()
    elseif effectType == 2 then
        CreateBlurVision()
    elseif effectType == 3 then
        CreateColorInversion()
    elseif effectType == 4 then
        CreateMultiplicity()
    end
end)
