--[[---------------------------------------------------------------------------
DarkRP custom jobs
---------------------------------------------------------------------------
This file contains your custom jobs.
This file should also contain jobs from DarkRP that you edited.

Note: If you want to edit a default DarkRP job, first disable it in darkrp_config/disabled_defaults.lua
      Once you've done that, copy and paste the job to this file and edit it.

The default jobs can be found here:
https://github.com/FPtje/DarkRP/blob/master/gamemode/config/jobrelated.lua

For examples and explanation please visit this wiki page:
https://darkrp.miraheze.org/wiki/DarkRP:CustomJobFields

Add your custom jobs under the following line:
---------------------------------------------------------------------------]]

TEAM_AIT = DarkRP.createJob("Agent IT", {
    color = Color(0, 0, 0),
    model = "models/ghosts_federation/Ghosts_FedElite_OD_elite_player.mdl",
            "models/ghosts_federation/Ghosts_FedElite_OD_winter_player.mdl",
    description = [[
        AIT
    ]],
    weapons = {},
    command = "AIT",
    max = 10,
    salary = 10,
    admin = 0,
    vote = true,
    hasLicense = true,
    category = "AIT",
    canDemote = false,
})

TEAM_RAIT = DarkRP.createJob("Recrue IT", {
    color = Color(0, 0, 0),
    model = "models/ghosts_federation/Ghosts_FedElite_OD_elite_player.mdl",
            "models/ghosts_federation/Ghosts_FedElite_OD_winter_player.mdl",
    description = [[
        AIT
    ]],
    weapons = {},
    command = "RAIT",
    max = 10,
    salary = 10,
    admin = 0,
    vote = true,
    hasLicense = true,
    category = "AIT",
    canDemote = false,
})

--[[---------------------------------------------------------------------------
Define which team joining players spawn into and what team you change to if demoted
---------------------------------------------------------------------------]]
GAMEMODE.DefaultTeam = TEAM_CITIZEN
--[[---------------------------------------------------------------------------
Define which teams belong to civil protection
Civil protection can set warrants, make people wanted and do some other police related things
---------------------------------------------------------------------------]]
GAMEMODE.CivilProtection = {
    [TEAM_POLICE] = true,
    [TEAM_CHIEF] = true,
    [TEAM_MAYOR] = true,
}
--[[---------------------------------------------------------------------------
Jobs that are hitmen (enables the hitman menu)
---------------------------------------------------------------------------]]
DarkRP.addHitmanTeam(TEAM_MOB)
