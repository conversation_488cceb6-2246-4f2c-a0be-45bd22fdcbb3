-- UTS MkVII Suit - Client Side
-- Interface HUD pour la combinaison tactique

-- Configuration client
UTS_SUIT_CLIENT = UTS_SUIT_CLIENT or {}
UTS_SUIT_CLIENT.Active = false
UTS_SUIT_CLIENT.ScanResults = {}

-- Couleurs de l'interface (thème noir/vert)
local COLOR_BG = Color(0, 0, 0, 200)
local COLOR_BORDER = Color(0, 255, 0, 255)
local COLOR_TEXT = Color(255, 255, 255, 255)
local COLOR_SCAN = Color(100, 255, 100, 255)

-- Fonction pour dessiner l'HUD
function UTS_SUIT_CLIENT:DrawHUD()
    if not self.Active then return end
    
    local scrW, scrH = ScrW(), ScrH()
    
    -- Panneau principal (position gauche)
    local panelW, panelH = 300, 200
    local x, y = 20, scrH/2 - panelH/2
    
    draw.RoundedBox(8, x, y, panelW, panelH, COLOR_BG)
    draw.RoundedBox(8, x, y, panelW, 30, COLOR_BORDER)
    
    -- Titre
    draw.SimpleText("UTS MkVII - SCAN TERRAIN", "DermaLarge", x + panelW/2, y + 15, COLOR_TEXT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Ligne de séparation
    draw.RoundedBox(0, x + 10, y + 35, panelW - 20, 2, COLOR_BORDER)
    
    -- Résultats de scan
    local yOffset = 45
    local maxResults = 8
    local resultCount = 0
    
    for i, result in ipairs(self.ScanResults) do
        if resultCount >= maxResults then break end
        
        local color = COLOR_SCAN
        if result.type == "player" then
            color = Color(255, 100, 100)
        elseif result.type == "npc" then
            color = Color(255, 255, 100)
        elseif result.type == "weapon" then
            color = Color(100, 100, 255)
        end
        
        local text = result.name .. " (" .. result.distance .. "m)"
        draw.SimpleText(text, "DermaDefault", x + 10, y + yOffset, color)
        
        yOffset = yOffset + 15
        resultCount = resultCount + 1
    end
    
    if #self.ScanResults == 0 then
        draw.SimpleText("Aucune entité détectée", "DermaDefault", x + 10, y + yOffset, COLOR_TEXT)
    elseif #self.ScanResults > maxResults then
        draw.SimpleText("... et " .. (#self.ScanResults - maxResults) .. " autres", "DermaDefault", x + 10, y + yOffset, COLOR_SCAN)
    end
end

-- Fonction pour activer la combinaison
function UTS_SUIT_CLIENT:Activate()
    self.Active = true
    
    -- Désactiver les autres HUDs
    if INFILTRATION_SUIT_CLIENT then INFILTRATION_SUIT_CLIENT.Active = false end
    if COMBAT_SUIT_CLIENT then COMBAT_SUIT_CLIENT.Active = false end
    if HEAVY_ARMOR_CLIENT then HEAVY_ARMOR_CLIENT.Active = false end
    
    chat.AddText(Color(0, 255, 0), "[UTS MkVII] ", Color(255, 255, 255), "Systèmes de scan activés")
end

-- Fonction pour désactiver la combinaison
function UTS_SUIT_CLIENT:Deactivate()
    self.Active = false
    self.ScanResults = {}
    
    chat.AddText(Color(255, 100, 100), "[UTS MkVII] ", Color(255, 255, 255), "Systèmes de scan désactivés")
end

-- Hook pour dessiner le HUD
hook.Add("HUDPaint", "UTS_DrawHUD", function()
    UTS_SUIT_CLIENT:DrawHUD()
end)

-- Désactiver automatiquement la combinaison quand le joueur meurt
hook.Add("PlayerDeath", "UTS_ClientPlayerDeath", function(victim, inflictor, attacker)
    if victim == LocalPlayer() and UTS_SUIT_CLIENT.Active then
        UTS_SUIT_CLIENT:Deactivate()
    end
end)

-- Netmessages
net.Receive("UTS_Equip", function()
    UTS_SUIT_CLIENT:Activate()
end)

net.Receive("UTS_Unequip", function()
    UTS_SUIT_CLIENT:Deactivate()
end)

net.Receive("UTS_ScanResults", function()
    local results = net.ReadTable()
    UTS_SUIT_CLIENT.ScanResults = results
end)

print("UTS MkVII Suit - Client loaded")
