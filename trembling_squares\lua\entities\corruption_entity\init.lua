AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

-- Network strings pour la communication client-serveur
util.AddNetworkString("CorruptPlayer")
util.AddNetworkString("CorruptionEnd")

-- Table globale pour stocker les joueurs corrompus
corruptedPlayers = corruptedPlayers or {}

function ENT:Initialize()
    self:SetModel("models/hunter/blocks/cube1x1x1.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetMaterial("models/debug/debugwhite")
    self:SetColor(Color(0, 0, 0))
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
    end
end

function ENT:Use(activator, caller)
    if activator:IsPlayer() then
        -- Afficher le message d'avertissement
        activator:PrintMessage(HUD_PRINTCENTER, "Vous n'auriez pas dû toucher...")

        -- <PERSON><PERSON> le joueur comme corrompu côté serveur
        if not corruptedPlayers then corruptedPlayers = {} end
        corruptedPlayers[activator:SteamID()] = {
            player = activator,
            startTime = CurTime()
        }

        net.Start("CorruptPlayer")
        net.Send(activator)
    end
end

function ENT:Touch(ent)
    if ent:IsPlayer() then
        -- Afficher le message d'avertissement
        ent:PrintMessage(HUD_PRINTCENTER, "Vous n'auriez pas dû toucher...")

        -- Marquer le joueur comme corrompu côté serveur
        if not corruptedPlayers then corruptedPlayers = {} end
        corruptedPlayers[ent:SteamID()] = {
            player = ent,
            startTime = CurTime()
        }

        net.Start("CorruptPlayer")
        net.Send(ent)
    end
end
