-- Hallucination Audio Effects
-- G<PERSON> les effets sonores des hallucinations

local hallucinationSounds = {
    "ambient/voices/crying_loop1.wav",
    "ambient/voices/m_scream1.wav", 
    "ambient/atmosphere/underground_hall_loop1.wav",
    "ambient/wind/wind_snippet1.wav",
    "ambient/creatures/town_zombie_call1.wav",
    "ambient/voices/playground_memory.wav",
    "ambient/atmosphere/cave_hit1.wav",
    "ambient/atmosphere/cave_hit2.wav",
    "ambient/atmosphere/cave_hit3.wav",
    "ambient/atmosphere/cave_hit4.wav"
}

local whisperSounds = {
    "vo/npc/male01/hacks01.wav",
    "vo/npc/male01/hacks02.wav", 
    "vo/npc/female01/hacks01.wav",
    "vo/npc/female01/hacks02.wav"
}

local currentAudioHallucination = nil
local audioHallucinationTimer = 0
local nextWhisperTime = 0

-- Fonction pour jouer un son d'hallucination
local function PlayHallucinationSound(soundPath, volume, pitch)
    volume = volume or 0.5
    pitch = pitch or 100
    
    if currentAudioHallucination then
        currentAudioHallucination:Stop()
    end
    
    currentAudioHallucination = CreateSound(LocalPlayer(), soundPath)
    if currentAudioHallucination then
        currentAudioHallucination:SetSoundLevel(0) -- Son local uniquement
        currentAudioHallucination:PlayEx(volume, pitch)
    end
end

-- Fonction pour jouer des chuchotements aléatoires
local function PlayRandomWhisper()
    if CurTime() < nextWhisperTime then return end
    
    local sound = whisperSounds[math.random(1, #whisperSounds)]
    local volume = math.Rand(0.1, 0.3)
    local pitch = math.random(80, 120)
    
    surface.PlaySound(sound)
    
    nextWhisperTime = CurTime() + math.random(5, 15)
end

-- Fonction pour créer des sons fantômes (sons qui semblent venir de nulle part)
local function CreateGhostSound()
    local sounds = hallucinationSounds
    local sound = sounds[math.random(1, #sounds)]
    local volume = math.Rand(0.2, 0.6)
    local pitch = math.random(70, 130)
    
    PlayHallucinationSound(sound, volume, pitch)
    
    -- Programmer l'arrêt du son après un délai aléatoire
    timer.Simple(math.random(2, 8), function()
        if currentAudioHallucination then
            currentAudioHallucination:FadeOut(2)
        end
    end)
end

-- Hook pour gérer les effets audio pendant les hallucinations
hook.Add("Think", "HallucinationAudioEffects", function()
    local ply = LocalPlayer()
    if not IsValid(ply) then return end
    
    -- Vérifier si le joueur est en hallucination audio
    if isHallucinating and currentHallucinationType == HALLUCINATION_TYPES.AUDIO_HALLUCINATION then
        -- Jouer des chuchotements occasionnels
        if math.random() < 0.001 then -- 0.1% de chance par frame
            PlayRandomWhisper()
        end
        
        -- Jouer des sons fantômes occasionnels
        if CurTime() > audioHallucinationTimer then
            CreateGhostSound()
            audioHallucinationTimer = CurTime() + math.random(10, 30)
        end
    end
end)

-- Network receiver pour les sons d'hallucination spécifiques
net.Receive("PlayHallucinationSound", function()
    local soundPath = net.ReadString()
    local volume = net.ReadFloat()
    local pitch = net.ReadFloat()
    
    PlayHallucinationSound(soundPath, volume, pitch)
end)

-- Fonction pour arrêter tous les sons d'hallucination
local function StopAllHallucinationSounds()
    if currentAudioHallucination then
        currentAudioHallucination:Stop()
        currentAudioHallucination = nil
    end
    
    audioHallucinationTimer = 0
    nextWhisperTime = 0
end

-- Hook pour arrêter les sons quand l'hallucination se termine
hook.Add("HallucinationStopped", "StopAudioHallucinations", function()
    StopAllHallucinationSounds()
end)
