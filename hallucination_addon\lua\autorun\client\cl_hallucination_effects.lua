-- Hallucination Visual Effects
-- Gère tous les effets visuels des hallucinations

-- Variables globales partagées entre les fichiers client
isHallucinating = false
hallucinationStartTime = 0
hallucinationDuration = 0
currentHallucinationType = 0
hallucinationIntensity = 1.0
fakeEntities = {}

-- Matériaux pour les effets
local distortionMaterial = Material("effects/water_warp01")
local staticMaterial = Material("effects/tvscreen_noise002a")

-- Variables pour les effets de distorsion
local distortionTime = 0
local screenShakeIntensity = 0

-- Fonction pour créer des entités factices
local function CreateFakeEntity(pos, model, color)
    local fakeEnt = {
        pos = pos,
        model = model or GetRandomFakeModel(),
        color = color or Color(255, 0, 0, 150),
        angles = Angle(0, math.random(0, 360), 0),
        scale = math.Rand(0.5, 2.0),
        bobOffset = math.random(0, 100),
        created = CurTime()
    }

    table.insert(fakeEntities, fakeEnt)
    return fakeEnt
end

-- Fonction pour dessiner les entités factices
local function DrawFakeEntities()
    for i, ent in ipairs(fakeEntities) do
        if CurTime() - ent.created > HALLUCINATION_CONFIG.FAKE_ENTITY_LIFETIME then
            table.remove(fakeEntities, i)
            continue
        end
        
        -- Animation de flottement
        local bobHeight = math.sin((CurTime() + ent.bobOffset) * 2) * 10
        local drawPos = ent.pos + Vector(0, 0, bobHeight)
        
        -- Rotation
        ent.angles.y = ent.angles.y + FrameTime() * 30
        
        -- Dessiner le modèle
        render.SetColorModulation(ent.color.r / 255, ent.color.g / 255, ent.color.b / 255)
        render.SetBlend(ent.color.a / 255)
        
        local matrix = Matrix()
        matrix:Translate(drawPos)
        matrix:Rotate(ent.angles)
        matrix:Scale(Vector(ent.scale, ent.scale, ent.scale))
        
        render.PushRenderTarget()
        render.ModelMaterialOverride(Material("models/wireframe"))
        render.SetModelMatrix(matrix)
        
        local model = Model(ent.model)
        if model then
            model:DrawModel()
        end
        
        render.SetModelMatrix()
        render.ModelMaterialOverride()
        render.PopRenderTarget()
        
        render.SetColorModulation(1, 1, 1)
        render.SetBlend(1)
    end
end

-- Effet de distorsion de l'écran
local function DrawScreenDistortion()
    if not isHallucinating then return end
    
    local scrW, scrH = ScrW(), ScrH()
    local time = CurTime() - hallucinationStartTime
    local intensity = hallucinationIntensity * math.sin(time * 2) * 0.5 + 0.5
    
    -- Effet de vagues
    render.SetMaterial(distortionMaterial)
    render.DrawScreenQuadEx(0, 0, scrW, scrH)
    
    -- Effet de bruit statique
    if math.random() < 0.1 * intensity then
        render.SetMaterial(staticMaterial)
        render.SetColorModulation(1, 1, 1)
        render.SetBlend(0.3 * intensity)
        render.DrawScreenQuadEx(0, 0, scrW, scrH)
        render.SetBlend(1)
    end
    
    -- Lignes de scan
    surface.SetDrawColor(255, 255, 255, 20 * intensity)
    for i = 0, scrH, 4 do
        surface.DrawLine(0, i + math.sin(time * 10 + i * 0.1) * 2, scrW, i + math.sin(time * 10 + i * 0.1) * 2)
    end
end

-- Effet de tremblement de l'écran
local function ApplyScreenShake()
    if not isHallucinating then return end

    local time = CurTime() - hallucinationStartTime
    local intensity = hallucinationIntensity * 0.1

    screenShakeIntensity = math.sin(time * 15) * intensity

    local shakeX = math.random(-screenShakeIntensity, screenShakeIntensity)
    local shakeY = math.random(-screenShakeIntensity, screenShakeIntensity)

    -- Appliquer le tremblement à la vue
    local viewData = {
        origin = LocalPlayer():EyePos() + Vector(shakeX, shakeY, 0),
        angles = LocalPlayer():EyeAngles() + Angle(shakeY * 0.5, shakeX * 0.5, 0)
    }

    return viewData
end

-- Hooks pour les effets
hook.Add("PostDrawOpaqueRenderables", "HallucinationFakeEntities", function()
    if isHallucinating and currentHallucinationType == HALLUCINATION_TYPES.FAKE_ENTITIES then
        DrawFakeEntities()
    end
end)

hook.Add("PostDrawHUD", "HallucinationScreenEffects", function()
    if isHallucinating and (currentHallucinationType == HALLUCINATION_TYPES.VISUAL_DISTORTION or
                           currentHallucinationType == HALLUCINATION_TYPES.SCREEN_EFFECTS) then
        DrawScreenDistortion()
    end
end)

hook.Add("CalcView", "HallucinationScreenShake", function(ply, pos, angles, fov)
    if isHallucinating and currentHallucinationType == HALLUCINATION_TYPES.VISUAL_DISTORTION then
        local shakeData = ApplyScreenShake()
        if shakeData then
            return shakeData
        end
    end
end)

-- Network receivers pour mettre à jour les variables
net.Receive("StartHallucination", function()
    local halluType = net.ReadInt(8)
    local duration = net.ReadFloat()
    local intensity = net.ReadFloat()

    isHallucinating = true
    hallucinationStartTime = CurTime()
    hallucinationDuration = duration
    currentHallucinationType = halluType
    hallucinationIntensity = intensity

    -- Créer des entités factices si nécessaire
    if halluType == HALLUCINATION_TYPES.FAKE_ENTITIES then
        local ply = LocalPlayer()
        local pos = ply:GetPos()

        for i = 1, math.random(3, 8) do
            local randomPos = pos + Vector(math.random(-500, 500), math.random(-500, 500), math.random(0, 100))
            CreateFakeEntity(randomPos, "models/props_c17/oildrum001.mdl", Color(255, 0, 0, 100))
        end
    end

    print("[Hallucination] Started - Type: " .. halluType .. ", Duration: " .. duration)
end)

net.Receive("StopHallucination", function()
    isHallucinating = false
    hallucinationStartTime = 0
    hallucinationDuration = 0
    currentHallucinationType = 0
    hallucinationIntensity = 1.0
    fakeEntities = {}

    print("[Hallucination] Stopped")
end)
