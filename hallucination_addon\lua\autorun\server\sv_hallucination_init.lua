-- Hallucination Addon - Server Initialization
-- Initialise les network strings et les variables globales

-- Network strings pour la communication client-serveur
util.AddNetworkString("StartHallucination")
util.AddNetworkString("StopHallucination")
util.AddNetworkString("HallucinationEffect")
util.AddNetworkString("SpawnFakeEntity")
util.AddNetworkString("RemoveFakeEntity")
util.AddNetworkString("PlayHallucinationSound")

-- Table globale pour stocker les joueurs en hallucination
HallucinatingPlayers = HallucinatingPlayers or {}

-- Types d'hallucinations disponibles
HALLUCINATION_TYPES = {
    VISUAL_DISTORTION = 1,
    FAKE_ENTITIES = 2,
    AUDIO_HALLUCINATION = 3,
    SCREEN_EFFECTS = 4,
    FAKE_PLAYERS = 5,
    PARANOIA_MODE = 6
}

-- Durées par défaut (en secondes)
HALLUCINATION_DURATIONS = {
    SHORT = 10,
    MEDIUM = 30,
    LONG = 60,
    EXTREME = 120
}

print("[Hallucination Addon] Server initialized successfully!")
