-- Test simple pour l'addon d'hallucination
-- Placez ce fichier dans lua/autorun/client/ pour tester

-- Commande de test côté client
concommand.Add("test_hallucination", function(ply, cmd, args)
    if not IsValid(LocalPlayer()) then return end
    
    print("=== Test Hallucination Addon ===")
    
    -- Vérifier que les variables globales existent
    print("isHallucinating:", isHallucinating or "UNDEFINED")
    print("HALLUCINATION_TYPES:", HALLUCINATION_TYPES and "DEFINED" or "UNDEFINED")
    print("HALLUCINATION_CONFIG:", HALLUCINATION_CONFIG and "DEFINED" or "UNDEFINED")
    
    -- Tester les fonctions globales
    if IsPlayerHallucinating then
        print("IsPlayerHallucinating():", IsPlayerHallucinating())
    else
        print("IsPlayerHallucinating: UNDEFINED")
    end
    
    if GetCurrentHallucinationType then
        print("GetCurrentHallucinationType():", GetCurrentHallucinationType())
    else
        print("GetCurrentHallucinationType: UNDEFINED")
    end
    
    print("=== Fin du test ===")
end)

print("[Hallucination Test] Fichier de test chargé. Utilisez 'test_hallucination' dans la console.")
