--[[---------------------------------------------------------------------------
Dark<PERSON> Agenda's
---------------------------------------------------------------------------
<PERSON><PERSON>'s can be set by the agenda manager and read by both the agenda manager and the other teams connected to it.


HOW TO MAKE AN AGENDA:
AddAgenda(Title of the agenda, Manager (who edits it), {Listeners (the ones who just see and follow the agenda)})
---------------------------------------------------------------------------]]
-- Example: AddAgenda("<PERSON><PERSON>'s agenda", TEAM_MOB, {TEAM_GANG})
-- Example: Add<PERSON><PERSON><PERSON>("Police agenda", TEAM_MAYOR, {TEAM_CHIEF, TEAM_POLICE})
