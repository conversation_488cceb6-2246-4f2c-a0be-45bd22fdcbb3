-- Hallucination Addon - Client Initialization
-- Initialise les variables côté client et les hooks

-- Variables globales côté client
local isHallucinating = false
local hallucinationStartTime = 0
local hallucinationDuration = 0
local currentHallucinationType = 0
local fakeEntities = {}
local hallucinationIntensity = 1.0

-- Matériaux pour les effets visuels
local distortionMaterial = Material("effects/water_warp01")
local staticMaterial = Material("effects/tvscreen_noise002a")

-- Sons d'hallucination
local hallucinationSounds = {
    "ambient/voices/crying_loop1.wav",
    "ambient/voices/m_scream1.wav",
    "ambient/atmosphere/underground_hall_loop1.wav",
    "ambient/wind/wind_snippet1.wav",
    "ambient/creatures/town_zombie_call1.wav"
}

-- Fonction pour démarrer une hallucination
local function StartHallucination(halluType, duration, intensity)
    isHallucinating = true
    hallucinationStartTime = CurTime()
    hallucinationDuration = duration or 30
    currentHallucinationType = halluType or HALLUCINATION_TYPES.VISUAL_DISTORTION
    hallucinationIntensity = intensity or 1.0
    
    print("[Hallucination] Started - Type: " .. halluType .. ", Duration: " .. duration)
end

-- Fonction pour arrêter une hallucination
local function StopHallucination()
    isHallucinating = false
    hallucinationStartTime = 0
    hallucinationDuration = 0
    currentHallucinationType = 0
    hallucinationIntensity = 1.0
    
    -- Nettoyer les entités factices
    for k, v in pairs(fakeEntities) do
        if IsValid(v) then
            v:Remove()
        end
    end
    fakeEntities = {}
    
    print("[Hallucination] Stopped")
end

-- Network receivers
net.Receive("StartHallucination", function()
    local halluType = net.ReadInt(8)
    local duration = net.ReadFloat()
    local intensity = net.ReadFloat()
    StartHallucination(halluType, duration, intensity)

    -- Déclencher le hook pour notifier les autres systèmes
    hook.Call("HallucinationStarted", nil, halluType, duration, intensity)
end)

net.Receive("StopHallucination", function()
    StopHallucination()

    -- Déclencher le hook pour notifier les autres systèmes
    hook.Call("HallucinationStopped", nil)
end)

net.Receive("HallucinationEffect", function()
    local effectType = net.ReadInt(8)
    local data = net.ReadTable()
    -- Traiter les effets spéciaux
    hook.Call("HallucinationSpecialEffect", nil, effectType, data)
end)

-- Fonction globale pour vérifier si le joueur est en hallucination
function IsPlayerHallucinating()
    return isHallucinating
end

-- Fonction globale pour obtenir le type d'hallucination actuel
function GetCurrentHallucinationType()
    return currentHallucinationType
end

-- Fonction globale pour obtenir l'intensité de l'hallucination
function GetHallucinationIntensity()
    return hallucinationIntensity
end

print("[Hallucination Addon] Client initialized successfully!")
