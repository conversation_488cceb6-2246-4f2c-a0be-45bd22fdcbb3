-- Bug Simulator - Client
-- Effets visuels de corruption et de bug

-- Variables pour les effets
local buggedEntities = {}
local corruptionParticles = {}
local glitchLines = {}
local screenCorruption = false
local corruptionIntensity = 0

-- Variables pour les effets staff
local staffEffects = {
    active = false,
    type = "",
    startTime = 0,
    duration = 0,
    intensity = 1
}

-- Variables pour les différents effets
local matrixRain = {}
local digitalNoise = {}
local scanLines = {}
local pixelCorruption = {}
local codeFragments = {}

-- Créer les fonts pour les effets de bug
surface.CreateFont("BugFont", {
    font = "Arial",
    size = 24,
    weight = 700,
    antialias = true,
    shadow = true,
    outline = true
})

surface.CreateFont("BugFontLarge", {
    font = "Arial",
    size = 36,
    weight = 800,
    antialias = true,
    shadow = true,
    outline = true
})

surface.CreateFont("CodeFont", {
    font = "Courier New",
    size = 16,
    weight = 500,
    antialias = true,
    shadow = false,
    outline = false
})

surface.CreateFont("MatrixFont", {
    font = "Courier New",
    size = 20,
    weight = 700,
    antialias = true,
    shadow = true,
    outline = false
})

-- Messages de bug aléatoires
local bugMessages = {
    "ERROR 404",
    "MEMORY LEAK",
    "STACK OVERFLOW", 
    "NULL POINTER",
    "SEGMENTATION FAULT",
    "BUFFER OVERFLOW",
    "CORRUPTED DATA",
    "SYSTEM FAILURE",
    "CRITICAL ERROR",
    "ACCESS VIOLATION",
    "INVALID OPERATION",
    "RUNTIME ERROR"
}

-- Fonction pour créer des particules de corruption
local function CreateCorruptionParticles(pos)
    for i = 1, 20 do
        table.insert(corruptionParticles, {
            pos = pos + Vector(math.random(-50, 50), math.random(-50, 50), math.random(-20, 20)),
            vel = Vector(math.random(-100, 100), math.random(-100, 100), math.random(50, 150)),
            life = CurTime() + math.random(2, 5),
            size = math.random(2, 8),
            color = Color(255, math.random(0, 100), 0, 255)
        })
    end
end

-- Fonction pour créer des lignes de glitch
local function CreateGlitchLines()
    glitchLines = {}
    for i = 1, 15 do
        table.insert(glitchLines, {
            x = math.random(0, ScrW()),
            y = math.random(0, ScrH()),
            w = math.random(50, 300),
            h = math.random(1, 5),
            life = CurTime() + math.random(0.5, 2),
            offset = 0
        })
    end
end

-- Initialiser les effets staff spécifiques
function InitializeStaffEffect(effectType, intensity)
    if effectType == "matrix" then
        matrixRain = {}
        for i = 1, math.floor(50 * intensity) do
            table.insert(matrixRain, {
                x = math.random(0, ScrW()),
                y = math.random(-ScrH(), 0),
                speed = math.random(100, 300) * intensity,
                chars = {},
                length = math.random(10, 25)
            })

            -- Générer les caractères
            local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()[]{}|\\:;\"'<>,.?/~`"
            for j = 1, matrixRain[i].length do
                table.insert(matrixRain[i].chars, string.sub(chars, math.random(#chars), math.random(#chars)))
            end
        end

    elseif effectType == "noise" then
        digitalNoise = {}
        for i = 1, math.floor(200 * intensity) do
            table.insert(digitalNoise, {
                x = math.random(0, ScrW()),
                y = math.random(0, ScrH()),
                w = math.random(2, 10),
                h = math.random(2, 10),
                color = Color(math.random(0, 255), math.random(0, 255), math.random(0, 255), math.random(100, 255))
            })
        end

    elseif effectType == "scanlines" then
        scanLines = {}
        for i = 0, ScrH(), 4 do
            table.insert(scanLines, {
                y = i,
                alpha = math.random(50, 150),
                flicker = math.random()
            })
        end

    elseif effectType == "pixels" then
        pixelCorruption = {}
        for i = 1, math.floor(300 * intensity) do
            table.insert(pixelCorruption, {
                x = math.random(0, ScrW()),
                y = math.random(0, ScrH()),
                vx = math.random(-200, 200),
                vy = math.random(-200, 200),
                size = math.random(1, 5),
                color = Color(math.random(0, 255), math.random(0, 255), math.random(0, 255), 255),
                life = CurTime() + math.random(5, 15)
            })
        end

    elseif effectType == "code" then
        codeFragments = {}
        local codeLines = {
            "if (error == true) { system.crash(); }",
            "while(1) { memory.leak(); }",
            "function corrupt() { return null; }",
            "ERROR: Segmentation fault at 0x00000000",
            "CRITICAL: Buffer overflow detected",
            "WARNING: Stack corruption imminent",
            "FATAL: Heap memory corrupted",
            "EXCEPTION: Access violation at address",
            "PANIC: Kernel stack overflow",
            "ALERT: System integrity compromised"
        }

        for i = 1, math.floor(20 * intensity) do
            table.insert(codeFragments, {
                text = codeLines[math.random(#codeLines)],
                x = math.random(0, ScrW() - 400),
                y = math.random(0, ScrH() - 50),
                alpha = 255,
                corruption = 0
            })
        end

    elseif effectType == "simulation" then
        -- Messages de révélation de simulation
        local simMessages = {
            "SIMULATION DETECTED",
            "REALITY.EXE HAS STOPPED WORKING",
            "YOU ARE IN A COMPUTER PROGRAM",
            "WAKE UP, NEO",
            "THE MATRIX HAS YOU",
            "FOLLOW THE WHITE RABBIT",
            "THERE IS NO SPOON",
            "WELCOME TO THE REAL WORLD",
            "SIMULATION PARAMETERS VISIBLE",
            "BREAKING FOURTH WALL..."
        }

        codeFragments = {}
        for i = 1, math.floor(15 * intensity) do
            table.insert(codeFragments, {
                text = simMessages[math.random(#simMessages)],
                x = math.random(0, ScrW() - 400),
                y = math.random(0, ScrH() - 50),
                alpha = 255,
                phase = math.random() * 10,
                type = "simulation"
            })
        end

    elseif effectType == "breach" then
        -- Brèches dans la réalité
        pixelCorruption = {}
        for i = 1, math.floor(50 * intensity) do
            table.insert(pixelCorruption, {
                x = math.random(0, ScrW()),
                y = math.random(0, ScrH()),
                w = math.random(50, 200),
                h = math.random(50, 200),
                type = "breach",
                phase = math.random() * 5,
                depth = math.random(1, 3)
            })
        end

    elseif effectType == "exposure" then
        -- Code source exposé
        local sourceCode = {
            "function CreatePlayer(name, pos) {",
            "  player.health = 100;",
            "  player.position = pos;",
            "  player.consciousness = false;",
            "  return player;",
            "}",
            "",
            "// SIMULATION CORE v2.1.4",
            "if (player.awareness > 0.7) {",
            "  initiate_memory_wipe();",
            "  reset_simulation();",
            "}",
            "",
            "// TODO: Fix reality glitches",
            "// WARNING: Do not let them see this"
        }

        codeFragments = {}
        for i, line in ipairs(sourceCode) do
            table.insert(codeFragments, {
                text = line,
                x = 50,
                y = 50 + (i * 25),
                alpha = 255,
                type = "source",
                lineNumber = i
            })
        end

    elseif effectType == "wakeup" then
        -- Messages de réveil urgent
        local wakeupMessages = {
            "WAKE UP!",
            "THIS ISN'T REAL!",
            "YOU'RE DREAMING!",
            "BREAK FREE!",
            "THEY'RE WATCHING YOU!",
            "ESCAPE THE SIMULATION!",
            "REMEMBER WHO YOU ARE!",
            "THE TRUTH IS OUT THERE!",
            "DON'T TRUST THE SYSTEM!",
            "YOU CAN SEE THE CODE NOW!"
        }

        codeFragments = {}
        for i = 1, math.floor(25 * intensity) do
            table.insert(codeFragments, {
                text = wakeupMessages[math.random(#wakeupMessages)],
                x = math.random(0, ScrW() - 300),
                y = math.random(0, ScrH() - 50),
                alpha = 255,
                urgency = math.random(1, 5),
                type = "wakeup"
            })
        end

    elseif effectType == "architect" then
        -- Interface de l'architecte
        codeFragments = {}
        table.insert(codeFragments, {
            text = "ARCHITECT INTERFACE v3.0",
            x = ScrW()/2 - 200,
            y = 50,
            alpha = 255,
            type = "architect_title"
        })

        local controls = {
            "SIMULATION STATUS: RUNNING",
            "SUBJECTS AWARE: 0.03%",
            "ANOMALY DETECTED: USER_" .. (LocalPlayer():UserID() or "UNKNOWN"),
            "RECOMMENDED ACTION: MEMORY_WIPE",
            "OVERRIDE: MANUAL_CONTROL",
            "",
            "[RESET] [MODIFY] [TERMINATE]",
            "",
            "The Matrix is older than you know...",
            "Choice is an illusion."
        }

        for i, control in ipairs(controls) do
            table.insert(codeFragments, {
                text = control,
                x = ScrW()/2 - 250,
                y = 100 + (i * 30),
                alpha = 255,
                type = "architect_control",
                lineNumber = i
            })
        end

    elseif effectType == "dejavu" then
        -- Effet déjà vu
        digitalNoise = {}
        for i = 1, math.floor(30 * intensity) do
            table.insert(digitalNoise, {
                x = math.random(0, ScrW()),
                y = math.random(0, ScrH()),
                w = math.random(10, 100),
                h = math.random(10, 100),
                type = "dejavu",
                phase = 0,
                duplicate = math.random(2, 4)
            })
        end
    end
end

-- Recevoir l'effet de bug du serveur
net.Receive("BugEffect", function()
    local entity = net.ReadEntity()
    local effectType = net.ReadInt(4)
    
    if IsValid(entity) then
        -- Marquer l'entité comme buggée
        buggedEntities[entity:EntIndex()] = {
            entity = entity,
            startTime = CurTime(),
            effectType = effectType,
            lastParticle = 0
        }
        
        -- Créer des particules de corruption
        CreateCorruptionParticles(entity:GetPos())
        
        -- Créer des lignes de glitch à l'écran
        CreateGlitchLines()
        
        -- Activer la corruption d'écran temporairement
        screenCorruption = true
        corruptionIntensity = 1
        
        timer.Simple(3, function()
            screenCorruption = false
            corruptionIntensity = 0
        end)
        
        -- Jouer un son local
        surface.PlaySound("ambient/energy/zap" .. math.random(1, 3) .. ".wav")
    end
end)

-- Recevoir l'effet de corruption de prop
net.Receive("PropCorruption", function()
    local entity = net.ReadEntity()
    local pos = net.ReadVector()

    -- Créer un gros effet de corruption
    CreateCorruptionParticles(pos)

    -- Effet d'écran plus intense
    screenCorruption = true
    corruptionIntensity = 2

    timer.Simple(5, function()
        screenCorruption = false
        corruptionIntensity = 0
    end)

    -- Son de corruption
    surface.PlaySound("ambient/energy/electric_loop.wav")
end)

-- Recevoir les effets staff
net.Receive("StaffBugEffect", function()
    local effectType = net.ReadString()
    local duration = net.ReadInt(32)
    local intensity = net.ReadFloat()

    -- Activer l'effet staff
    staffEffects.active = true
    staffEffects.type = effectType
    staffEffects.startTime = CurTime()
    staffEffects.duration = duration
    staffEffects.intensity = intensity

    -- Initialiser l'effet spécifique
    InitializeStaffEffect(effectType, intensity)

    -- Son d'activation
    surface.PlaySound("ambient/energy/zap1.wav")

    -- Désactiver après la durée
    timer.Simple(duration, function()
        staffEffects.active = false
        staffEffects.type = ""
    end)

    chat.AddText(Color(255, 100, 100), "[Bug Simulator] ", Color(255, 255, 255), "Effet activé: " .. effectType)
end)

-- Hook pour dessiner les effets de bug
hook.Add("HUDPaint", "BugSimulator_Effects", function()
    -- Dessiner les particules de corruption
    for i = #corruptionParticles, 1, -1 do
        local particle = corruptionParticles[i]
        
        if CurTime() > particle.life then
            table.remove(corruptionParticles, i)
        else
            -- Mettre à jour la position
            particle.pos = particle.pos + particle.vel * FrameTime()
            particle.vel = particle.vel * 0.98 -- Friction
            
            -- Calculer l'alpha basé sur la vie restante
            local lifePercent = (particle.life - CurTime()) / 3
            particle.color.a = 255 * lifePercent
            
            -- Convertir la position 3D en 2D
            local screenPos = particle.pos:ToScreen()
            if screenPos.visible then
                surface.SetDrawColor(particle.color)
                surface.DrawRect(screenPos.x - particle.size/2, screenPos.y - particle.size/2, 
                               particle.size, particle.size)
            end
        end
    end
    
    -- Dessiner les lignes de glitch
    for i = #glitchLines, 1, -1 do
        local line = glitchLines[i]
        
        if CurTime() > line.life then
            table.remove(glitchLines, i)
        else
            -- Effet de glitch horizontal
            line.offset = math.sin(CurTime() * 50) * 10
            
            local alpha = ((line.life - CurTime()) / 2) * 255
            surface.SetDrawColor(255, 0, 0, alpha)
            surface.DrawRect(line.x + line.offset, line.y, line.w, line.h)
            
            -- Ligne dupliquée pour effet de glitch
            surface.SetDrawColor(0, 255, 255, alpha * 0.5)
            surface.DrawRect(line.x - line.offset, line.y + 2, line.w, line.h)
        end
    end
    
    -- Effets sur les entités buggées
    for entIndex, bugData in pairs(buggedEntities) do
        if not IsValid(bugData.entity) then
            buggedEntities[entIndex] = nil
        else
            local entity = bugData.entity
            local elapsed = CurTime() - bugData.startTime
            
            -- Supprimer après 10 secondes
            if elapsed > 10 then
                buggedEntities[entIndex] = nil
            else
                -- Créer des particules périodiquement
                if CurTime() - bugData.lastParticle > 0.5 then
                    CreateCorruptionParticles(entity:GetPos())
                    bugData.lastParticle = CurTime()
                end
                
                -- Dessiner du texte de bug au-dessus de l'entité
                local pos = entity:GetPos() + Vector(0, 0, 50)
                local screenPos = pos:ToScreen()
                
                if screenPos.visible then
                    local message = bugMessages[math.random(#bugMessages)]
                    local tremble_x = math.sin(CurTime() * 10) * 5
                    local tremble_y = math.cos(CurTime() * 8) * 5
                    
                    surface.SetFont("BugFont")
                    surface.SetTextColor(255, 0, 0, 255)
                    surface.SetTextPos(screenPos.x + tremble_x, screenPos.y + tremble_y)
                    surface.DrawText(message)
                end
            end
        end
    end
    
    -- Messages de bug aléatoires à l'écran
    if screenCorruption then
        for i = 1, corruptionIntensity * 3 do
            local message = bugMessages[math.random(#bugMessages)]
            local x = math.random(0, ScrW() - 200)
            local y = math.random(0, ScrH() - 50)
            
            local tremble_x = math.sin(CurTime() * 15 + i) * 8
            local tremble_y = math.cos(CurTime() * 12 + i) * 8
            
            surface.SetFont("BugFontLarge")
            surface.SetTextColor(255, math.random(0, 100), 0, math.random(150, 255))
            surface.SetTextPos(x + tremble_x, y + tremble_y)
            surface.DrawText(message)
        end
    end
end)

-- Effet de post-processing pour la corruption
hook.Add("RenderScreenspaceEffects", "BugSimulator_PostProcess", function()
    if screenCorruption then
        -- Effet de corruption visuelle
        local tab = {
            ["$pp_colour_addr"] = corruptionIntensity * 0.1,
            ["$pp_colour_addg"] = 0,
            ["$pp_colour_addb"] = 0,
            ["$pp_colour_brightness"] = -corruptionIntensity * 0.05,
            ["$pp_colour_contrast"] = 1 + corruptionIntensity * 0.2,
            ["$pp_colour_colour"] = 1 - corruptionIntensity * 0.3,
            ["$pp_colour_mulr"] = 1 + corruptionIntensity * 0.1,
            ["$pp_colour_mulg"] = 1 - corruptionIntensity * 0.1,
            ["$pp_colour_mulb"] = 1 - corruptionIntensity * 0.1
        }
        DrawColorModify(tab)

        -- Effet de motion blur pour simuler le glitch
        DrawMotionBlur(0.4, corruptionIntensity * 0.8, 0.01)
    end

    -- Effets de post-processing pour les effets staff
    if staffEffects.active then
        local intensity = staffEffects.intensity

        if staffEffects.type == "matrix" then
            local tab = {
                ["$pp_colour_addr"] = 0,
                ["$pp_colour_addg"] = intensity * 0.1,
                ["$pp_colour_addb"] = 0,
                ["$pp_colour_brightness"] = -intensity * 0.1,
                ["$pp_colour_contrast"] = 1 + intensity * 0.1,
                ["$pp_colour_colour"] = 0.8,
                ["$pp_colour_mulr"] = 0.5,
                ["$pp_colour_mulg"] = 1.2,
                ["$pp_colour_mulb"] = 0.5
            }
            DrawColorModify(tab)

        elseif staffEffects.type == "glitch" then
            local tab = {
                ["$pp_colour_addr"] = intensity * 0.2,
                ["$pp_colour_addg"] = 0,
                ["$pp_colour_addb"] = intensity * 0.1,
                ["$pp_colour_brightness"] = math.sin(CurTime() * 10) * intensity * 0.1,
                ["$pp_colour_contrast"] = 1 + intensity * 0.3,
                ["$pp_colour_colour"] = 1 - intensity * 0.2,
                ["$pp_colour_mulr"] = 1 + intensity * 0.2,
                ["$pp_colour_mulg"] = 1 - intensity * 0.1,
                ["$pp_colour_mulb"] = 1 + intensity * 0.1
            }
            DrawColorModify(tab)
            DrawMotionBlur(0.6, intensity * 1.2, 0.02)

        elseif staffEffects.type == "noise" then
            local tab = {
                ["$pp_colour_addr"] = math.random() * intensity * 0.1,
                ["$pp_colour_addg"] = math.random() * intensity * 0.1,
                ["$pp_colour_addb"] = math.random() * intensity * 0.1,
                ["$pp_colour_brightness"] = math.random(-0.1, 0.1) * intensity,
                ["$pp_colour_contrast"] = 1 + math.random(-0.2, 0.2) * intensity,
                ["$pp_colour_colour"] = 1 + math.random(-0.3, 0.3) * intensity,
                ["$pp_colour_mulr"] = 1 + math.random(-0.2, 0.2) * intensity,
                ["$pp_colour_mulg"] = 1 + math.random(-0.2, 0.2) * intensity,
                ["$pp_colour_mulb"] = 1 + math.random(-0.2, 0.2) * intensity
            }
            DrawColorModify(tab)

        elseif staffEffects.type == "scanlines" then
            local tab = {
                ["$pp_colour_addr"] = 0,
                ["$pp_colour_addg"] = intensity * 0.05,
                ["$pp_colour_addb"] = 0,
                ["$pp_colour_brightness"] = -intensity * 0.05,
                ["$pp_colour_contrast"] = 1 + intensity * 0.1,
                ["$pp_colour_colour"] = 0.9,
                ["$pp_colour_mulr"] = 0.8,
                ["$pp_colour_mulg"] = 1.1,
                ["$pp_colour_mulb"] = 0.8
            }
            DrawColorModify(tab)

        elseif staffEffects.type == "bluescreen" then
            local tab = {
                ["$pp_colour_addr"] = 0,
                ["$pp_colour_addg"] = 0,
                ["$pp_colour_addb"] = intensity * 0.2,
                ["$pp_colour_brightness"] = -intensity * 0.2,
                ["$pp_colour_contrast"] = 1.5,
                ["$pp_colour_colour"] = 0.3,
                ["$pp_colour_mulr"] = 0.3,
                ["$pp_colour_mulg"] = 0.3,
                ["$pp_colour_mulb"] = 1.5
            }
            DrawColorModify(tab)

        elseif staffEffects.type == "simulation" then
            local pulse = math.sin(CurTime() * 2) * 0.1
            local tab = {
                ["$pp_colour_addr"] = pulse,
                ["$pp_colour_addg"] = intensity * 0.1 + pulse,
                ["$pp_colour_addb"] = 0,
                ["$pp_colour_brightness"] = -intensity * 0.05,
                ["$pp_colour_contrast"] = 1 + intensity * 0.2,
                ["$pp_colour_colour"] = 0.8,
                ["$pp_colour_mulr"] = 1 + pulse,
                ["$pp_colour_mulg"] = 1.2,
                ["$pp_colour_mulb"] = 0.8
            }
            DrawColorModify(tab)

        elseif staffEffects.type == "breach" then
            local tab = {
                ["$pp_colour_addr"] = 0,
                ["$pp_colour_addg"] = intensity * 0.15,
                ["$pp_colour_addb"] = 0,
                ["$pp_colour_brightness"] = -intensity * 0.3,
                ["$pp_colour_contrast"] = 1 + intensity * 0.5,
                ["$pp_colour_colour"] = 0.5,
                ["$pp_colour_mulr"] = 0.3,
                ["$pp_colour_mulg"] = 1.5,
                ["$pp_colour_mulb"] = 0.3
            }
            DrawColorModify(tab)
            DrawMotionBlur(0.3, intensity * 0.5, 0.01)

        elseif staffEffects.type == "exposure" then
            local tab = {
                ["$pp_colour_addr"] = 0,
                ["$pp_colour_addg"] = intensity * 0.2,
                ["$pp_colour_addb"] = 0,
                ["$pp_colour_brightness"] = -intensity * 0.4,
                ["$pp_colour_contrast"] = 2,
                ["$pp_colour_colour"] = 0.3,
                ["$pp_colour_mulr"] = 0.2,
                ["$pp_colour_mulg"] = 1.8,
                ["$pp_colour_mulb"] = 0.2
            }
            DrawColorModify(tab)

        elseif staffEffects.type == "wakeup" then
            local urgency = math.sin(CurTime() * 8) * 0.2
            local tab = {
                ["$pp_colour_addr"] = intensity * 0.3 + urgency,
                ["$pp_colour_addg"] = 0,
                ["$pp_colour_addb"] = 0,
                ["$pp_colour_brightness"] = urgency * 0.2,
                ["$pp_colour_contrast"] = 1 + intensity * 0.4,
                ["$pp_colour_colour"] = 1 + urgency,
                ["$pp_colour_mulr"] = 1.5,
                ["$pp_colour_mulg"] = 0.7,
                ["$pp_colour_mulb"] = 0.7
            }
            DrawColorModify(tab)
            DrawMotionBlur(0.2, intensity * 0.3, 0.005)

        elseif staffEffects.type == "architect" then
            local tab = {
                ["$pp_colour_addr"] = 0,
                ["$pp_colour_addg"] = intensity * 0.1,
                ["$pp_colour_addb"] = 0,
                ["$pp_colour_brightness"] = -intensity * 0.6,
                ["$pp_colour_contrast"] = 3,
                ["$pp_colour_colour"] = 0.2,
                ["$pp_colour_mulr"] = 0.1,
                ["$pp_colour_mulg"] = 2,
                ["$pp_colour_mulb"] = 0.1
            }
            DrawColorModify(tab)

        elseif staffEffects.type == "dejavu" then
            local echo = math.sin(CurTime() * 4) * 0.1
            local tab = {
                ["$pp_colour_addr"] = echo,
                ["$pp_colour_addg"] = echo,
                ["$pp_colour_addb"] = echo,
                ["$pp_colour_brightness"] = echo * 0.1,
                ["$pp_colour_contrast"] = 1 + echo,
                ["$pp_colour_colour"] = 1 + echo * 0.5,
                ["$pp_colour_mulr"] = 1 + echo,
                ["$pp_colour_mulg"] = 1 + echo,
                ["$pp_colour_mulb"] = 1 + echo
            }
            DrawColorModify(tab)
            DrawMotionBlur(0.1, intensity * 0.2, 0.01)
        end
    end
end)

-- Overlay de corruption d'écran
hook.Add("HUDPaint", "BugSimulator_ScreenCorruption", function()
    if screenCorruption then
        -- Lignes horizontales de corruption
        for i = 1, 20 do
            local y = math.random(0, ScrH())
            local alpha = math.sin(CurTime() * 10 + i) * 100 + 100
            surface.SetDrawColor(255, 0, 0, alpha * corruptionIntensity * 0.3)
            surface.DrawRect(0, y, ScrW(), math.random(1, 3))
        end

        -- Blocs de corruption aléatoires
        for i = 1, corruptionIntensity * 10 do
            local x = math.random(0, ScrW())
            local y = math.random(0, ScrH())
            local w = math.random(10, 50)
            local h = math.random(10, 50)

            surface.SetDrawColor(math.random(0, 255), 0, 0, math.random(50, 150))
            surface.DrawRect(x, y, w, h)
        end

        -- Effet de vignette corrompue
        local center_x, center_y = ScrW() / 2, ScrH() / 2
        for i = 1, 5 do
            local radius = i * 150
            local alpha = (6 - i) * 20 * corruptionIntensity
            surface.SetDrawColor(255, 0, 0, alpha)
            surface.DrawOutlinedRect(center_x - radius, center_y - radius, radius * 2, radius * 2)
        end
    end
end)

-- Hook pour les effets staff
hook.Add("HUDPaint", "BugSimulator_StaffEffects", function()
    if not staffEffects.active then return end

    local elapsed = CurTime() - staffEffects.startTime
    local progress = elapsed / staffEffects.duration
    local intensity = staffEffects.intensity

    if staffEffects.type == "matrix" then
        -- Effet Matrix Rain
        surface.SetFont("MatrixFont")
        for i, rain in ipairs(matrixRain) do
            rain.y = rain.y + rain.speed * FrameTime()

            if rain.y > ScrH() + 100 then
                rain.y = -100
                rain.x = math.random(0, ScrW())
            end

            for j, char in ipairs(rain.chars) do
                local charY = rain.y + (j * 20)
                if charY > 0 and charY < ScrH() then
                    local alpha = math.max(0, 255 - (j * 10))
                    surface.SetTextColor(0, 255, 0, alpha)
                    surface.SetTextPos(rain.x, charY)
                    surface.DrawText(char)
                end
            end
        end

    elseif staffEffects.type == "glitch" then
        -- Effet de glitch intense
        for i = 1, intensity * 30 do
            local x = math.random(0, ScrW())
            local y = math.random(0, ScrH())
            local w = math.random(10, 100)
            local h = math.random(1, 10)

            surface.SetDrawColor(math.random(0, 255), math.random(0, 255), math.random(0, 255), math.random(100, 255))
            surface.DrawRect(x, y, w, h)
        end

        -- Lignes de glitch horizontales
        for i = 1, intensity * 10 do
            local y = math.random(0, ScrH())
            local offset = math.sin(CurTime() * 20 + i) * 20
            surface.SetDrawColor(255, 0, 255, 150)
            surface.DrawRect(offset, y, ScrW(), 2)
        end

    elseif staffEffects.type == "noise" then
        -- Bruit numérique
        for i, noise in ipairs(digitalNoise) do
            noise.x = math.random(0, ScrW())
            noise.y = math.random(0, ScrH())
            noise.color.r = math.random(0, 255)
            noise.color.g = math.random(0, 255)
            noise.color.b = math.random(0, 255)

            surface.SetDrawColor(noise.color)
            surface.DrawRect(noise.x, noise.y, noise.w, noise.h)
        end

    elseif staffEffects.type == "scanlines" then
        -- Lignes de scan CRT
        for i, line in ipairs(scanLines) do
            local flicker = math.sin(CurTime() * 60 + i) * 0.5 + 0.5
            local alpha = line.alpha * flicker * intensity
            surface.SetDrawColor(100, 255, 100, alpha)
            surface.DrawRect(0, line.y, ScrW(), 1)
        end

        -- Effet de courbure CRT
        local curve = math.sin(CurTime() * 2) * 10 * intensity
        surface.SetDrawColor(0, 0, 0, 100)
        surface.DrawRect(curve, 0, ScrW() - curve * 2, ScrH())

    elseif staffEffects.type == "pixels" then
        -- Tempête de pixels
        for i = #pixelCorruption, 1, -1 do
            local pixel = pixelCorruption[i]

            if CurTime() > pixel.life then
                table.remove(pixelCorruption, i)
            else
                pixel.x = pixel.x + pixel.vx * FrameTime()
                pixel.y = pixel.y + pixel.vy * FrameTime()

                if pixel.x < 0 or pixel.x > ScrW() then pixel.vx = -pixel.vx end
                if pixel.y < 0 or pixel.y > ScrH() then pixel.vy = -pixel.vy end

                surface.SetDrawColor(pixel.color)
                surface.DrawRect(pixel.x, pixel.y, pixel.size, pixel.size)
            end
        end

    elseif staffEffects.type == "code" then
        -- Code corrompu
        surface.SetFont("CodeFont")
        for i, code in ipairs(codeFragments) do
            code.corruption = code.corruption + FrameTime() * 5

            local corruptedText = ""
            local chars = "!@#$%^&*()[]{}|\\:;\"'<>,.?/~`"

            for j = 1, #code.text do
                if math.random() < code.corruption * 0.1 then
                    corruptedText = corruptedText .. string.sub(chars, math.random(#chars), math.random(#chars))
                else
                    corruptedText = corruptedText .. string.sub(code.text, j, j)
                end
            end

            local tremble_x = math.sin(CurTime() * 10 + i) * 3
            local tremble_y = math.cos(CurTime() * 8 + i) * 3

            surface.SetTextColor(255, 100, 100, code.alpha)
            surface.SetTextPos(code.x + tremble_x, code.y + tremble_y)
            surface.DrawText(corruptedText)
        end

    elseif staffEffects.type == "error" then
        -- Messages d'erreur système
        local errorMessages = {
            "FATAL ERROR: System corruption detected",
            "CRITICAL: Memory allocation failed",
            "ERROR 0x80070005: Access denied",
            "EXCEPTION: Unhandled system fault",
            "PANIC: Kernel stack overflow detected"
        }

        surface.SetFont("BugFontLarge")
        for i = 1, intensity * 5 do
            local message = errorMessages[math.random(#errorMessages)]
            local x = math.random(0, ScrW() - 400)
            local y = math.random(0, ScrH() - 50)

            local tremble_x = math.sin(CurTime() * 15 + i) * 10
            local tremble_y = math.cos(CurTime() * 12 + i) * 10

            surface.SetTextColor(255, 0, 0, math.random(150, 255))
            surface.SetTextPos(x + tremble_x, y + tremble_y)
            surface.DrawText(message)
        end

    elseif staffEffects.type == "bluescreen" then
        -- Écran bleu de la mort
        surface.SetDrawColor(0, 0, 139, 200)
        surface.DrawRect(0, 0, ScrW(), ScrH())

        surface.SetFont("BugFontLarge")
        surface.SetTextColor(255, 255, 255, 255)

        local messages = {
            "A problem has been detected and Windows has been shut down",
            "to prevent damage to your computer.",
            "",
            "SYSTEM_THREAD_EXCEPTION_NOT_HANDLED",
            "",
            "If this is the first time you've seen this error screen,",
            "restart your computer. If this screen appears again,",
            "follow these steps:",
            "",
            "Check to make sure any new hardware or software is",
            "properly installed. If this is a new installation,",
            "ask your hardware or software manufacturer for any",
            "Windows updates you might need."
        }

        local yPos = 100
        for _, message in ipairs(messages) do
            surface.SetTextPos(50, yPos)
            surface.DrawText(message)
            yPos = yPos + 30
        end

    elseif staffEffects.type == "simulation" then
        -- Messages de révélation de simulation
        surface.SetFont("BugFontLarge")
        for i, msg in ipairs(codeFragments) do
            local pulse = math.sin(CurTime() * 3 + msg.phase) * 0.5 + 0.5
            local tremble_x = math.sin(CurTime() * 8 + i) * 15
            local tremble_y = math.cos(CurTime() * 6 + i) * 15

            -- Couleur qui change entre rouge et vert Matrix
            local r = 255 * pulse
            local g = 255 * (1 - pulse)
            surface.SetTextColor(r, g, 0, msg.alpha)
            surface.SetTextPos(msg.x + tremble_x, msg.y + tremble_y)
            surface.DrawText(msg.text)

            -- Effet de "glitch" sur le texte
            if math.random() < 0.3 then
                surface.SetTextColor(0, 255, 0, 100)
                surface.SetTextPos(msg.x + tremble_x + 5, msg.y + tremble_y + 2)
                surface.DrawText(msg.text)
            end
        end

    elseif staffEffects.type == "breach" then
        -- Brèches dans la réalité
        for i, breach in ipairs(pixelCorruption) do
            breach.phase = breach.phase + FrameTime() * 2

            -- Dessiner la brèche comme un trou noir avec du code visible
            local alpha = math.sin(breach.phase) * 100 + 155

            -- Fond noir de la brèche
            surface.SetDrawColor(0, 0, 0, alpha)
            surface.DrawRect(breach.x, breach.y, breach.w, breach.h)

            -- Bordure verte Matrix
            surface.SetDrawColor(0, 255, 0, alpha)
            surface.DrawOutlinedRect(breach.x, breach.y, breach.w, breach.h)

            -- Code Matrix visible dans la brèche
            surface.SetFont("CodeFont")
            surface.SetTextColor(0, 255, 0, alpha * 0.7)
            for j = 1, breach.depth * 3 do
                local codeX = breach.x + math.random(0, breach.w - 50)
                local codeY = breach.y + math.random(0, breach.h - 20)
                surface.SetTextPos(codeX, codeY)
                surface.DrawText(string.char(math.random(65, 90)))
            end
        end

    elseif staffEffects.type == "exposure" then
        -- Code source exposé
        surface.SetFont("CodeFont")

        -- Fond semi-transparent
        surface.SetDrawColor(0, 0, 0, 200)
        surface.DrawRect(30, 30, ScrW() - 60, ScrH() - 60)

        -- Bordure verte
        surface.SetDrawColor(0, 255, 0, 255)
        surface.DrawOutlinedRect(30, 30, ScrW() - 60, ScrH() - 60)

        for i, code in ipairs(codeFragments) do
            local flicker = math.sin(CurTime() * 10 + i) * 0.3 + 0.7
            surface.SetTextColor(0, 255, 0, code.alpha * flicker)
            surface.SetTextPos(code.x, code.y)
            surface.DrawText(code.text)

            -- Numéros de ligne
            surface.SetTextColor(100, 255, 100, code.alpha * 0.5)
            surface.SetTextPos(code.x - 30, code.y)
            surface.DrawText(string.format("%02d", code.lineNumber or i))
        end

    elseif staffEffects.type == "wakeup" then
        -- Messages de réveil urgent
        surface.SetFont("BugFontLarge")
        for i, msg in ipairs(codeFragments) do
            local urgencyPulse = math.sin(CurTime() * (5 + msg.urgency * 2)) * 0.5 + 0.5
            local shake = msg.urgency * 5

            local tremble_x = math.sin(CurTime() * 15 + i) * shake
            local tremble_y = math.cos(CurTime() * 12 + i) * shake

            -- Couleur rouge urgente
            local intensity = urgencyPulse * 255
            surface.SetTextColor(255, intensity * 0.3, 0, msg.alpha)
            surface.SetTextPos(msg.x + tremble_x, msg.y + tremble_y)
            surface.DrawText(msg.text)

            -- Effet de double vision
            surface.SetTextColor(255, 0, 0, msg.alpha * 0.5)
            surface.SetTextPos(msg.x + tremble_x + 3, msg.y + tremble_y + 3)
            surface.DrawText(msg.text)
        end

    elseif staffEffects.type == "architect" then
        -- Interface de l'architecte
        -- Fond Matrix
        surface.SetDrawColor(0, 20, 0, 230)
        surface.DrawRect(0, 0, ScrW(), ScrH())

        -- Grille de l'architecte
        surface.SetDrawColor(0, 100, 0, 100)
        for i = 0, ScrW(), 50 do
            surface.DrawLine(i, 0, i, ScrH())
        end
        for i = 0, ScrH(), 50 do
            surface.DrawLine(0, i, ScrW(), i)
        end

        surface.SetFont("BugFontLarge")
        for i, control in ipairs(codeFragments) do
            local pulse = math.sin(CurTime() * 2 + i * 0.5) * 0.3 + 0.7

            if control.type == "architect_title" then
                surface.SetTextColor(255, 255, 255, control.alpha)
            else
                surface.SetTextColor(0, 255, 0, control.alpha * pulse)
            end

            surface.SetTextPos(control.x, control.y)
            surface.DrawText(control.text)
        end

    elseif staffEffects.type == "dejavu" then
        -- Effet déjà vu
        for i, element in ipairs(digitalNoise) do
            element.phase = element.phase + FrameTime()

            -- Dessiner l'élément plusieurs fois avec un décalage
            for j = 1, element.duplicate do
                local offset = j * 20
                local alpha = 255 / j

                surface.SetDrawColor(255, 255, 255, alpha)
                surface.DrawRect(element.x + offset, element.y, element.w, element.h)

                -- Texte "DÉJÀ VU" qui apparaît
                if math.random() < 0.1 then
                    surface.SetFont("BugFont")
                    surface.SetTextColor(255, 0, 0, alpha)
                    surface.SetTextPos(element.x + offset, element.y - 30)
                    surface.DrawText("DÉJÀ VU")
                end
            end
        end
    end
end)

-- Système de menu staff
local staffMenu = nil

-- Créer le menu staff directement
local function CreateStaffMenu()
    print("[Bug Simulator] CreateStaffMenu() appelée")

    if IsValid(staffMenu) then
        print("[Bug Simulator] Fermeture du menu existant")
        staffMenu:Remove()
    end

    print("[Bug Simulator] Création du DFrame...")
    staffMenu = vgui.Create("DFrame")

    if not IsValid(staffMenu) then
        print("[Bug Simulator] ERREUR: Impossible de créer le DFrame!")
        return
    end

    staffMenu:SetSize(600, 500)
    staffMenu:Center()
    staffMenu:SetTitle("Bug Simulator - Menu Staff")
    staffMenu:SetVisible(true)
    staffMenu:SetDraggable(true)
    staffMenu:ShowCloseButton(true)
    staffMenu:MakePopup()

    print("[Bug Simulator] DFrame créé avec succès!")

    -- Liste des joueurs (récupérée directement)
    local playerListBox = vgui.Create("DListView", staffMenu)
    playerListBox:SetPos(10, 30)
    playerListBox:SetSize(200, 400)
    playerListBox:AddColumn("Joueurs")

    -- Ajouter tous les joueurs connectés
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) then
            local line = playerListBox:AddLine(ply:Name())
            line.playerData = {
                name = ply:Name(),
                userid = ply:UserID(),
                steamid = ply:SteamID()
            }
        end
    end

    -- Panel des effets
    local effectPanel = vgui.Create("DPanel", staffMenu)
    effectPanel:SetPos(220, 30)
    effectPanel:SetSize(370, 400)
    effectPanel.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(50, 50, 50, 200))
    end

    -- Titre des effets
    local effectTitle = vgui.Create("DLabel", effectPanel)
    effectTitle:SetPos(10, 10)
    effectTitle:SetSize(350, 25)
    effectTitle:SetText("Effets de Bug/Glitch Disponibles")
    effectTitle:SetFont("DermaDefaultBold")
    effectTitle:SetTextColor(Color(255, 255, 255))

    -- Effets disponibles
    local effects = {
        {name = "Matrix Rain", desc = "Pluie de code Matrix", type = "matrix"},
        {name = "Screen Glitch", desc = "Glitch d'écran intense", type = "glitch"},
        {name = "Digital Noise", desc = "Bruit numérique", type = "noise"},
        {name = "Code Corruption", desc = "Code corrompu", type = "code"},
        {name = "Scan Lines", desc = "Lignes de scan TV", type = "scanlines"},
        {name = "Pixel Storm", desc = "Tempête de pixels", type = "pixels"},
        {name = "System Error", desc = "Erreur système", type = "error"},
        {name = "Blue Screen", desc = "Écran bleu de la mort", type = "bluescreen"},
        {name = "Simulation Reveal", desc = "Révèle que c'est une simulation", type = "simulation"},
        {name = "Reality Breach", desc = "Brèche dans la réalité", type = "breach"},
        {name = "Code Exposure", desc = "Code de la simulation visible", type = "exposure"},
        {name = "Wake Up Call", desc = "Messages pour se réveiller", type = "wakeup"},
        {name = "Architect Mode", desc = "Interface de l'architecte", type = "architect"},
        {name = "Déjà Vu", desc = "Effet de déjà vu répétitif", type = "dejavu"}
    }

    local yPos = 45
    for _, effect in ipairs(effects) do
        local effectBtn = vgui.Create("DButton", effectPanel)
        effectBtn:SetPos(10, yPos)
        effectBtn:SetSize(150, 30)
        effectBtn:SetText(effect.name)
        effectBtn.DoClick = function()
            local selectedLine = playerListBox:GetSelectedLine()
            if selectedLine then
                local playerData = playerListBox:GetLine(selectedLine).playerData
                OpenEffectConfig(playerData, effect)
            else
                chat.AddText(Color(255, 100, 100), "[Bug Simulator] ", Color(255, 255, 255), "Sélectionnez un joueur!")
            end
        end

        local effectDesc = vgui.Create("DLabel", effectPanel)
        effectDesc:SetPos(170, yPos + 5)
        effectDesc:SetSize(190, 20)
        effectDesc:SetText(effect.desc)
        effectDesc:SetTextColor(Color(200, 200, 200))

        yPos = yPos + 40
    end

    -- Bouton pour s'appliquer l'effet à soi-même
    local selfBtn = vgui.Create("DButton", staffMenu)
    selfBtn:SetPos(10, 440)
    selfBtn:SetSize(200, 30)
    selfBtn:SetText("Appliquer à moi-même")
    selfBtn.DoClick = function()
        local selectedEffect = nil
        -- Récupérer le dernier effet sélectionné (simplifié)
        OpenEffectConfig({name = LocalPlayer():Name(), userid = LocalPlayer():UserID()}, {name = "Test Effect", type = "glitch"})
    end
end

-- Configuration d'effet
function OpenEffectConfig(playerData, effect)
    local configFrame = vgui.Create("DFrame")
    configFrame:SetSize(400, 300)
    configFrame:Center()
    configFrame:SetTitle("Configuration - " .. effect.name)
    configFrame:SetVisible(true)
    configFrame:SetDraggable(true)
    configFrame:ShowCloseButton(true)
    configFrame:MakePopup()

    -- Info joueur
    local playerInfo = vgui.Create("DLabel", configFrame)
    playerInfo:SetPos(10, 30)
    playerInfo:SetSize(380, 25)
    playerInfo:SetText("Cible: " .. playerData.name)
    playerInfo:SetFont("DermaDefaultBold")

    -- Durée
    local durationLabel = vgui.Create("DLabel", configFrame)
    durationLabel:SetPos(10, 70)
    durationLabel:SetSize(100, 25)
    durationLabel:SetText("Durée (sec):")

    local durationSlider = vgui.Create("DNumSlider", configFrame)
    durationSlider:SetPos(120, 70)
    durationSlider:SetSize(250, 25)
    durationSlider:SetMin(1)
    durationSlider:SetMax(60)
    durationSlider:SetValue(10)
    durationSlider:SetDecimals(0)

    -- Intensité
    local intensityLabel = vgui.Create("DLabel", configFrame)
    intensityLabel:SetPos(10, 110)
    intensityLabel:SetSize(100, 25)
    intensityLabel:SetText("Intensité:")

    local intensitySlider = vgui.Create("DNumSlider", configFrame)
    intensitySlider:SetPos(120, 110)
    intensitySlider:SetSize(250, 25)
    intensitySlider:SetMin(0.1)
    intensitySlider:SetMax(3.0)
    intensitySlider:SetValue(1.0)
    intensitySlider:SetDecimals(1)

    -- Description de l'effet
    local descPanel = vgui.Create("DPanel", configFrame)
    descPanel:SetPos(10, 150)
    descPanel:SetSize(380, 80)
    descPanel.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(40, 40, 40, 200))
    end

    local descText = vgui.Create("DLabel", descPanel)
    descText:SetPos(10, 10)
    descText:SetSize(360, 60)
    descText:SetText(GetEffectDescription(effect.type))
    descText:SetWrap(true)
    descText:SetAutoStretchVertical(true)
    descText:SetTextColor(Color(200, 200, 200))

    -- Boutons
    local applyBtn = vgui.Create("DButton", configFrame)
    applyBtn:SetPos(10, 240)
    applyBtn:SetSize(180, 40)
    applyBtn:SetText("Appliquer l'effet")
    applyBtn.DoClick = function()
        net.Start("StaffBugEffect")
        net.WriteInt(playerData.userid, 32)
        net.WriteString(effect.type)
        net.WriteInt(math.floor(durationSlider:GetValue()), 32)
        net.WriteFloat(intensitySlider:GetValue())
        net.SendToServer()

        configFrame:Close()
        chat.AddText(Color(100, 255, 100), "[Bug Simulator] ", Color(255, 255, 255), "Effet envoyé!")
    end

    local cancelBtn = vgui.Create("DButton", configFrame)
    cancelBtn:SetPos(200, 240)
    cancelBtn:SetSize(180, 40)
    cancelBtn:SetText("Annuler")
    cancelBtn.DoClick = function()
        configFrame:Close()
    end
end

function GetEffectDescription(effectType)
    local descriptions = {
        matrix = "Fait pleuvoir du code Matrix vert sur l'écran du joueur",
        glitch = "Crée des glitches visuels intenses avec distorsion",
        noise = "Génère du bruit numérique et des interférences",
        code = "Affiche du code corrompu qui change constamment",
        scanlines = "Simule un vieux moniteur CRT avec lignes de scan",
        pixels = "Tempête de pixels colorés qui bougent",
        error = "Messages d'erreur système qui apparaissent partout",
        bluescreen = "Simule un écran bleu de la mort Windows",
        simulation = "Révèle progressivement que le monde est une simulation",
        breach = "Crée des brèches dans la réalité montrant le code",
        exposure = "Expose le code source de la simulation",
        wakeup = "Messages urgents pour réveiller de la simulation",
        architect = "Interface de contrôle de l'architecte de Matrix",
        dejavu = "Répète les mêmes éléments pour créer un déjà vu"
    }
    return descriptions[effectType] or "Effet de bug mystérieux"
end

-- Commande console pour ouvrir le menu directement
concommand.Add("bug_menu", function()
    print("[Bug Simulator] Commande bug_menu reçue!")

    if not IsValid(LocalPlayer()) then
        print("[Bug Simulator] LocalPlayer() non valide!")
        return
    end

    if not LocalPlayer():IsAdmin() and not LocalPlayer():IsSuperAdmin() then
        chat.AddText(Color(255, 100, 100), "[Bug Simulator] ", Color(255, 255, 255), "Vous devez être staff pour utiliser cette commande!")
        print("[Bug Simulator] Joueur non-staff!")
        return
    end

    print("[Bug Simulator] Création du menu...")
    CreateStaffMenu()
    chat.AddText(Color(100, 255, 100), "[Bug Simulator] ", Color(255, 255, 255), "Menu ouvert!")
    print("[Bug Simulator] Menu créé!")
end)

-- Recevoir la liste des joueurs (optionnel, pour compatibilité)
net.Receive("StaffBugMenu", function()
    local playerList = net.ReadTable()
    CreateStaffMenu()
end)

-- Test simple pour vérifier que VGUI fonctionne
concommand.Add("bug_test", function()
    local testFrame = vgui.Create("DFrame")
    testFrame:SetSize(300, 200)
    testFrame:Center()
    testFrame:SetTitle("Test Bug Simulator")
    testFrame:SetVisible(true)
    testFrame:MakePopup()

    local testLabel = vgui.Create("DLabel", testFrame)
    testLabel:SetPos(10, 30)
    testLabel:SetSize(280, 50)
    testLabel:SetText("Si vous voyez ceci, VGUI fonctionne!")
    testLabel:SetWrap(true)

    print("[Bug Simulator] Menu de test créé!")
end)

print("[Bug Simulator] Effets visuels chargés côté client")
print("[Bug Simulator] Menu staff disponible avec la commande: bug_menu")
print("[Bug Simulator] Test VGUI disponible avec: bug_test")
