-- UTS Spawner Entity - Client Side

include("shared.lua")

function ENT:Initialize()
    self.NextParticle = 0
    self.GlowSprite = Material("sprites/light_glow02_add")
end

function ENT:Draw()
    self:DrawModel()
    
    local pos = self:GetPos() + Vector(0, 0, 80)
    local ang = LocalPlayer():EyeAngles()
    
    if not ang then ang = Angle(0, 0, 0) end
    
    local drawAng = Angle(ang.p, ang.y, ang.r)
    drawAng:RotateAroundAxis(drawAng:Forward(), 90)
    drawAng:RotateAroundAxis(drawAng:Right(), 90)
    
    cam.Start3D2D(pos, drawAng, 0.2)
        draw.SimpleTextOutlined("UTS MkVII", "DermaLarge", 0, 0, Color(0, 255, 0), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 2, Color(0, 0, 0))
        draw.SimpleTextOutlined("Combinaison Tactique", "DermaDefault", 0, 30, Color(100, 255, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
        draw.SimpleTextOutlined("Scan: Terrain | Armure: +100", "DermaDefault", 0, 50, Color(200, 200, 200), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
        draw.SimpleTextOutlined("Portée: 1000u | Auto-scan", "DermaDefault", 0, 65, Color(200, 200, 200), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
        draw.SimpleTextOutlined("Appuyez sur E pour équiper", "DermaDefault", 0, 90, Color(100, 255, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
    cam.End3D2D()
    
    if CurTime() > self.NextParticle then
        self.NextParticle = CurTime() + 1
        
        local effectdata = EffectData()
        effectdata:SetOrigin(self:GetPos() + Vector(0, 0, 40))
        effectdata:SetScale(0.3)
        util.Effect("TeslaHitBoxes", effectdata)
    end
end

function ENT:Think()
    local pulse = math.sin(CurTime() * 2) * 0.3 + 0.7
    local baseColor = Color(50 * pulse, 50 * pulse, 50 * pulse, 255)
    self:SetColor(baseColor)
    
    self:SetNextClientThink(CurTime() + 0.1)
    return true
end

function ENT:DrawTranslucent()
    if not self.GlowSprite then return end
    
    render.SetMaterial(self.GlowSprite)
    render.DrawSprite(self:GetPos() + Vector(0, 0, 40), 100, 100, Color(0, 255, 0, 150))
    render.DrawSprite(self:GetPos() + Vector(20, 0, 20), 30, 30, Color(0, 200, 0, 100))
    render.DrawSprite(self:GetPos() + Vector(-20, 0, 20), 30, 30, Color(0, 200, 0, 100))
end
