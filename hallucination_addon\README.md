# Hallucination Effects Addon pour <PERSON>'s Mod

## Description
Cet addon ajoute des effets d'hallucination immersifs à <PERSON>'s Mod. Il permet de créer diverses expériences hallucinatoires pour les joueurs, incluant des distorsions visuelles, des entités factices, des effets audio et des modes de paranoïa.

## Fonctionnalités

### Types d'hallucinations disponibles :
1. **Distorsion Visuelle** - Tremblements d'écran, effets de distorsion
2. **Entités Factices** - Objets qui apparaissent et disparaissent
3. **Hallucinations Audio** - Sons fantômes, chuchotements, voix
4. **Effets d'Écran** - Bruit statique, lignes de scan, interférences
5. **Faux Joueurs** - Joueurs fantômes qui apparaissent brièvement
6. **Mode Paranoïa** - Combine plusieurs effets pour une expérience intense

## Installation
1. Placez le dossier `hallucination_addon` dans votre répertoire `garrysmod/addons/`
2. Redémarrez votre serveur Garry's Mod
3. L'addon sera automatiquement chargé

## Utilisation

### Entité Trigger
- Spawner l'entité "Hallucination Trigger" depuis le menu des entités
- Les joueurs qui touchent ou utilisent cette entité déclencheront une hallucination
- L'entité est configurable via les propriétés réseau

### Commandes Admin (Console)

#### Démarrer une hallucination :
```
hallucination_start <joueur> [type] [durée] [intensité]
```
- `joueur` : Nom du joueur cible
- `type` : Type d'hallucination (1-6, optionnel, défaut: 1)
- `durée` : Durée en secondes (optionnel, défaut: 30)
- `intensité` : Intensité de 0.1 à 2.0 (optionnel, défaut: 1.0)

**Exemples :**
```
hallucination_start John 1 60 1.5
hallucination_start Player 6 120 2.0
```

#### Arrêter une hallucination :
```
hallucination_stop <joueur>
```

#### Lister les joueurs en hallucination :
```
hallucination_list
```

### Types d'hallucinations détaillés :

1. **VISUAL_DISTORTION (1)** : 
   - Tremblements d'écran
   - Distorsions visuelles
   - Effets de vagues

2. **FAKE_ENTITIES (2)** :
   - Objets qui apparaissent aléatoirement
   - Entités semi-transparentes
   - Mouvement et rotation automatiques

3. **AUDIO_HALLUCINATION (3)** :
   - Sons d'ambiance inquiétants
   - Chuchotements aléatoires
   - Voix fantômes

4. **SCREEN_EFFECTS (4)** :
   - Bruit statique
   - Lignes de scan
   - Interférences visuelles

5. **FAKE_PLAYERS (5)** :
   - Joueurs fantômes
   - Apparitions brèves
   - Mouvements réalistes

6. **PARANOIA_MODE (6)** :
   - Combine tous les effets
   - Messages de paranoïa
   - Expérience la plus intense

## Configuration

Le fichier `lua/autorun/sh_hallucination_config.lua` contient toutes les options configurables :

- Durées des hallucinations
- Intensité des effets
- Messages personnalisés
- Sons utilisés
- Modèles d'entités factices

## Permissions

Seuls les administrateurs peuvent utiliser les commandes console pour déclencher des hallucinations sur d'autres joueurs.

## Compatibilité

- Compatible avec tous les gamemodes de Garry's Mod
- Fonctionne en multijoueur
- Optimisé pour les performances

## Support

Cet addon a été créé par l'IA Assistant. Pour des modifications ou des améliorations, vous pouvez éditer les fichiers Lua selon vos besoins.

## Avertissement

Utilisez cet addon de manière responsable. Les effets d'hallucination peuvent être désorientants pour certains joueurs. Assurez-vous d'avoir le consentement des joueurs avant d'utiliser les effets les plus intenses.
